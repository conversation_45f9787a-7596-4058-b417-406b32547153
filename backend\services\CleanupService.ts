import { PrismaClient } from '@prisma/client';
import { RedisService } from './redisService';
import '../utils/logger'; // 导入logger配置

/**
 * 会话清理服务 - 异步处理会话结束后的清理任务
 * 解决会话生命周期管理与数据库操作的时序竞态问题
 */
export class CleanupService {
  private prisma: PrismaClient;
  private redis: RedisService;
  private isRunning: boolean = false;
  private processingPromise: Promise<void> | null = null;
  private readonly QUEUE_NAME = 'session-cleanup-queue';
  private readonly LOCK_PREFIX = 'lock:session:';
  private readonly LOCK_TIMEOUT = 30000; // 30秒锁超时

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = RedisService.getInstance();
  }

  /**
   * 启动清理服务
   */
  public start(): void {
    if (this.isRunning) {
      logger.warn('CleanupService is already running');
      return;
    }

    this.isRunning = true;
    console.log('🧹 CleanupService started, waiting for cleanup jobs...');
    this.processingPromise = this.processQueue();
  }

  /**
   * 停止清理服务
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    console.log('🛑 CleanupService stopping...');

    if (this.processingPromise) {
      await this.processingPromise;
    }

    console.log('✅ CleanupService stopped');
  }

  /**
   * 添加会话清理任务到队列
   */
  public async addCleanupTask(sessionId: string): Promise<void> {
    try {
      const redisClient = this.redis.getClient();
      await redisClient.lpush(this.QUEUE_NAME, sessionId);
      console.log(`📤 Added cleanup task for session: ${sessionId}`);
    } catch (error) {
      console.error(`❌ Failed to add cleanup task for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 处理清理队列的主循环
   */
  private async processQueue(): Promise<void> {
    const redisClient = this.redis.getClient();

    while (this.isRunning) {
      try {
        // 阻塞式地从队列中取出一个sessionId，超时时间1秒
        const result = await redisClient.brpop(this.QUEUE_NAME, 1);
        
        if (!result) {
          // 超时，继续循环
          continue;
        }

        const sessionId = result[1]; // brpop返回[key, value]
        console.log(`🔄 Processing cleanup for session: ${sessionId}`);

        // 使用分布式锁，确保同一个session只被处理一次
        const lockAcquired = await this.acquireLock(sessionId);
        if (!lockAcquired) {
          console.warn(`⚠️ Failed to acquire lock for session ${sessionId}, skipping`);
          continue;
        }

        try {
          await this.performCleanup(sessionId);
          console.log(`✅ Cleanup completed for session: ${sessionId}`);
        } catch (error) {
          console.error(`❌ Failed to cleanup session ${sessionId}:`, error);
          // 可以考虑加入重试或死信队列逻辑
        } finally {
          await this.releaseLock(sessionId);
        }

      } catch (error) {
        if (this.isRunning) {
          console.error('❌ Error in cleanup queue processing:', error);
          // 短暂等待后继续
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  }

  /**
   * 执行实际的清理操作
   */
  private async performCleanup(sessionId: string): Promise<void> {
    // 在一个事务中完成所有数据库操作
    await this.prisma.$transaction(async (tx) => {
      // 1. 检查会话是否存在且状态为closing
      const session = await tx.interviewSession.findUnique({
        where: { id: sessionId },
        select: { id: true, status: true, userId: true }
      });

      if (!session) {
        console.warn(`⚠️ Session ${sessionId} not found, skipping cleanup`);
        return;
      }

      if (session.status === 'completed') {
        console.log(`ℹ️ Session ${sessionId} already completed, skipping cleanup`);
        return;
      }

      // 2. 更新会话状态为 'completed'
      await tx.interviewSession.update({
        where: { id: sessionId },
        data: {
          status: 'completed',
          endedAt: new Date()
        },
      });

      // 3. 保存完整的面试转录（如果有临时存储在Redis中）
      await this.saveTranscripts(sessionId, tx);

      console.log(`📊 Session ${sessionId} marked as completed in database`);
    }, {
      timeout: 10000, // 10秒超时
    });

    // 4. 清理所有相关的Redis缓存
    await this.cleanupRedisCache(sessionId);
  }

  /**
   * 保存面试转录记录
   */
  private async saveTranscripts(sessionId: string, tx: any): Promise<void> {
    try {
      const redisClient = this.redis.getClient();
      const transcriptKey = `transcript:${sessionId}`;
      
      // 获取转录数据
      const transcripts = await redisClient.lrange(transcriptKey, 0, -1);
      
      if (transcripts && transcripts.length > 0) {
        const parsedTranscripts = transcripts.map(t => {
          try {
            return JSON.parse(t);
          } catch (error) {
            console.warn(`⚠️ Failed to parse transcript for session ${sessionId}:`, error);
            return null;
          }
        }).filter(Boolean);

        if (parsedTranscripts.length > 0) {
          await tx.interviewTranscript.createMany({
            data: parsedTranscripts,
            skipDuplicates: true // 避免重复插入
          });
          console.log(`💾 Saved ${parsedTranscripts.length} transcripts for session ${sessionId}`);
        }
      }
    } catch (error) {
      console.error(`❌ Failed to save transcripts for session ${sessionId}:`, error);
      // 转录保存失败不应该阻止会话完成
    }
  }

  /**
   * 清理Redis缓存
   */
  private async cleanupRedisCache(sessionId: string): Promise<void> {
    try {
      const redisClient = this.redis.getClient();
      const keysToDelete = [
        `transcript:${sessionId}`,
        `session:${sessionId}:status`,
        `session:${sessionId}:data`
      ];

      for (const key of keysToDelete) {
        await redisClient.del(key);
      }

      console.log(`🧹 Cleaned up Redis cache for session ${sessionId}`);
    } catch (error) {
      console.error(`❌ Failed to cleanup Redis cache for session ${sessionId}:`, error);
    }
  }

  /**
   * 获取分布式锁
   */
  private async acquireLock(sessionId: string): Promise<boolean> {
    try {
      const lockKey = `${this.LOCK_PREFIX}${sessionId}`;
      const lockValue = `${Date.now()}_${Math.random()}`;
      const redisClient = this.redis.getClient();

      // 使用SET NX EX命令获取锁
      const result = await redisClient.set(lockKey, lockValue, 'PX', this.LOCK_TIMEOUT, 'NX');
      return result === 'OK';
    } catch (error) {
      console.error(`❌ Failed to acquire lock for session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * 释放分布式锁
   */
  private async releaseLock(sessionId: string): Promise<void> {
    try {
      const lockKey = `${this.LOCK_PREFIX}${sessionId}`;
      const redisClient = this.redis.getClient();
      await redisClient.del(lockKey);
    } catch (error) {
      console.error(`❌ Failed to release lock for session ${sessionId}:`, error);
    }
  }
}

export default CleanupService;
