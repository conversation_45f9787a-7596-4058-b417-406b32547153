{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "面试君项目", "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:admin-frontend": "cd admin-frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build:frontend": "cd frontend && npm run build", "build:admin-frontend": "cd admin-frontend && npm run build", "build:backend": "cd backend && npm run build", "start:frontend": "cd frontend && npm run preview", "start:admin-frontend": "cd admin-frontend && npm run preview", "start:backend": "cd backend && npm start", "build:verify": "cd frontend && npm run build:verify", "clean": "cd frontend && npm run clean", "test:local": "cd frontend && npm run test:local", "verify": "cd frontend && npm run verify"}, "workspaces": ["frontend", "backend", "admin-frontend"], "dependencies": {"recordrtc": "^5.6.2"}}