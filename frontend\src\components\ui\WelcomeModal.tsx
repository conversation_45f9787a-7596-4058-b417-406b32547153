import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Users, Heart } from 'lucide-react';

interface WelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const WelcomeModal: React.FC<WelcomeModalProps> = ({
  isOpen,
  onClose
}) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={handleClose}
          />
          
          {/* 弹窗内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
            className="relative bg-white dark:bg-gray-800 bg-opacity-95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-md mx-4"
          >
            {/* 关闭按钮 */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>

            {/* 头部区域 */}
            <div className="p-6 pb-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 flex-shrink-0">
                  <Heart className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    欢迎加入面试君！
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    感谢您的信任与选择
                  </p>
                </div>
              </div>

              {/* 介绍文字 */}
              <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-6">
                为了更好地为您服务，请扫码加入我们的用户服务群，获取最新功能更新和专业面试指导。
              </p>
            </div>

            {/* 二维码区域 */}
            <div className="px-6 pb-4">
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 text-center">
                {/* 群信息 */}
                <div className="flex items-center justify-center gap-2 mb-3">
                  <Users className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    面试君用户服务群-3
                  </span>
                </div>

                {/* 二维码容器 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-3 inline-block">
                  <img 
                    src="/images/service-group-qr.png" 
                    alt="面试君用户服务群二维码"
                    className="w-32 h-32 mx-auto"
                    onError={(e) => {
                      // 如果图片加载失败，显示占位符
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  {/* 占位符 */}
                  <div className="hidden w-32 h-32 mx-auto bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Users className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-xs text-gray-500 dark:text-gray-400">二维码加载中...</p>
                    </div>
                  </div>
                </div>

                {/* 提示文字 */}
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  用微信或企业微信扫码加入
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500">
                  该二维码8月19日前有效
                </p>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="p-6 pt-2">
              <button
                onClick={handleClose}
                className="w-full py-3 px-4 rounded-lg font-medium transition-all bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-[1.02]"
              >
                我知道了
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default WelcomeModal;
