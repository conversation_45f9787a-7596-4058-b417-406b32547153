// 面试会话管理器 - 核心架构类
import { WebSocketManager } from './WebSocketManager';
import { AudioManager } from './AudioManager';
import { CreditsManager } from './CreditsManager';
import { useInterviewSessionStore } from '../stores/interviewSessionStore';
import { generateSessionId, InterviewType } from '../utils/sessionIdGenerator';

export type SessionPhase = 'idle' | 'warming' | 'preparing' | 'ready' | 'active' | 'error';

export interface InterviewConfig {
  selectedPositionId: string;
  interviewLanguage: string;
  answerStyle: string;
  interviewType: 'mock' | 'formal';
  screenShareStatus?: string;
  sharedStream?: MediaStream;
  audioCollection?: boolean;
}

export interface SessionProgress {
  webSocket: number;
  audio: number;
  credits: number;
  overall: number;
}

export interface ResourceStatus {
  status: 'idle' | 'preparing' | 'ready' | 'error';
  error?: string;
  lastUpdated: number;
}

export interface SessionState {
  phase: SessionPhase;
  progress: SessionProgress;
  resources: {
    webSocket: ResourceStatus;
    audio: ResourceStatus;
    credits: ResourceStatus;
  };
  config?: InterviewConfig;
  sessionId?: string;
  startTime?: number;
  sessionInfo?: {
    sessionId: string;
    status: string;
    position: {
      companyName: string | null;
      positionName: string | null;
      titleJobInfo: string | null;
    };
  };
}

/**
 * 面试会话管理器 - 单例模式
 * 负责管理整个面试会话的生命周期，从预热到激活
 */
export class InterviewSessionManager {
  private static instance: InterviewSessionManager;
  private webSocketManager: WebSocketManager;
  private audioManager: AudioManager;
  private creditsManager: CreditsManager;
  private state: SessionState;

  private constructor() {
    this.webSocketManager = new WebSocketManager();
    this.audioManager = new AudioManager();
    this.creditsManager = new CreditsManager();

    this.state = {
      phase: 'idle',
      progress: {
        webSocket: 0,
        audio: 0,
        credits: 0,
        overall: 0
      },
      resources: {
        webSocket: { status: 'idle', lastUpdated: Date.now() },
        audio: { status: 'idle', lastUpdated: Date.now() },
        credits: { status: 'idle', lastUpdated: Date.now() }
      }
    };

    // 🔧 修复：监听WebSocket消息发送事件
    if (typeof window !== 'undefined') {
      window.addEventListener('websocket-send-message', this.handleWebSocketSendMessage.bind(this));
    }

    // 同步状态到Zustand store
    this.syncStateToStore();
  }

  public static getInstance(): InterviewSessionManager {
    if (!InterviewSessionManager.instance) {
      InterviewSessionManager.instance = new InterviewSessionManager();
    }
    return InterviewSessionManager.instance;
  }

  /**
   * 简化初始化 - 移除预热逻辑，改为空操作
   */
  public async warmUp(): Promise<void> {
    console.log('🔧 InterviewSessionManager: Warm-up disabled (using on-demand initialization)');

    // 直接设置为就绪状态，不进行任何预热操作
    this.updatePhase('ready');
    this.updateOverallProgress();

    console.log('✅ InterviewSessionManager: Ready (no warm-up needed)');
  }

  /**
   * 阶段2：准备 - 用户完成基本配置后
   */
  public async prepare(config: InterviewConfig): Promise<void> {
    console.log('🔧 InterviewSessionManager: Starting preparation phase', config);
    this.updatePhase('preparing');
    this.state.config = config;

    try {
      // 生成会话ID
      const sessionId = this.generateSessionId(config);
      this.state.sessionId = sessionId;

      // 🔥 修复时序问题：对于正式面试，先创建数据库记录，再建立WebSocket连接
      const mode = config.interviewType === 'mock' ? 'mock' : 'live';

      if (config.interviewType === 'formal') {
        // 正式面试：顺序执行以避免时序问题

        // 1. 先并行执行不依赖数据库的任务（跳过扣费，API会处理）
        const independentTasks = [
          this.audioManager.setupProcessors(config).then(() => this.updateResourceProgress('audio', 100))
        ];

        await Promise.all(independentTasks);
        this.updateResourceProgress('credits', 100); // 标记为完成，实际扣费在API中

        // 2. 然后创建数据库记录（通过API调用，包含扣费）
        await this.startFormalInterviewWithAtomicAPI();

        // 3. 最后建立WebSocket连接（此时数据库记录已存在）
        await this.webSocketManager.createSession(sessionId, config, mode);
        this.updateResourceProgress('webSocket', 100);

      } else {
        // 模拟面试：串行执行，先创建数据库记录
        // 1. 先并行执行独立任务
        const independentTasks = [
          this.audioManager.setupProcessors(config).then(() => this.updateResourceProgress('audio', 100))
        ];

        await Promise.all(independentTasks);
        this.updateResourceProgress('credits', 100); // 标记为完成，实际扣费在API中

        // 2. 然后创建数据库记录（通过API调用，包含扣费）
        await this.startMockInterviewWithAtomicAPI(config);

        // 3. 最后建立WebSocket连接（此时数据库记录已存在）
        await this.webSocketManager.createSession(sessionId, config, mode);
        this.updateResourceProgress('webSocket', 100);
      }
      
      this.updatePhase('ready');
      console.log('✅ InterviewSessionManager: Preparation phase completed');
    } catch (error) {
      console.error('❌ InterviewSessionManager: Preparation failed:', error);
      this.updatePhase('error');
      throw error;
    }
  }

  /**
   * 阶段3：验证和启动 - 用户点击开始面试（重构版本）
   */
  public async start(): Promise<void> {
    console.log('🚀 InterviewSessionManager: Starting validation and launch');

    try {
      // 快速验证所有资源就绪状态
      this.validateReadiness();

      // 🔥 修复：跳过前端扣费，后端API会统一处理扣费
      console.log('✅ InterviewSessionManager: Skipping frontend deduction, backend API will handle all deductions');

      console.log('✅ InterviewSessionManager: Validation and deduction completed, ready for activation');
    } catch (error) {
      console.error('❌ InterviewSessionManager: Start validation failed:', error);
      this.updatePhase('error');
      throw error;
    }
  }

  /**
   * 🔥 新增：使用原子性API开始模拟面试
   */
  private async startMockInterviewWithAtomicAPI(config: InterviewConfig): Promise<void> {
    console.log('🔥 InterviewSessionManager: Starting mock interview with atomic API');

    try {
      // 动态导入API服务
      const { interviewService } = await import('../lib/api/apiService');

      // 调用原子性开始面试API
      const response = await interviewService.startMockInterview({
        sessionId: this.state.sessionId!,
        config: {
          companyName: config.companyName || '模拟公司',
          positionName: config.positionName || '模拟岗位',
          interviewLanguage: 'chinese',
          answerStyle: 'conversational'
        }
      });

      if (response.success) {
        console.log('✅ InterviewSessionManager: Mock interview started successfully:', response.data);

        // 更新本地余额状态
        await this.creditsManager.refreshBalance();

        // 存储会话信息
        this.state.sessionInfo = {
          sessionId: response.data.sessionId,
          status: response.data.status,
          position: response.data.position
        };
      } else {
        throw new Error(response.message || '启动模拟面试失败');
      }
    } catch (error: any) {
      console.error('❌ InterviewSessionManager: Mock interview atomic API call failed:', error);

      // 处理特定错误类型
      if (error.message && error.message.includes('模拟面试次数不足')) {
        throw new Error(`INSUFFICIENT_CREDITS:${error.message}`);
      }

      throw error;
    }
  }

  /**
   * 🔥 新增：使用原子性API开始正式面试
   */
  private async startFormalInterviewWithAtomicAPI(): Promise<void> {
    console.log('🔥 InterviewSessionManager: Starting formal interview with atomic API');

    try {
      // 动态导入API服务
      const { interviewService } = await import('../lib/api/apiService');

      // 调用原子性开始面试API
      const response = await interviewService.startFormalInterview({
        sessionId: this.state.sessionId!,
        positionId: this.state.config!.selectedPositionId || undefined
      });

      if (response.success) {
        console.log('✅ InterviewSessionManager: Formal interview started successfully:', response.data);

        // 更新本地余额状态（如果有余额管理器）
        await this.creditsManager.refreshBalance();

        // 存储会话信息
        this.state.sessionInfo = {
          sessionId: response.data.sessionId,
          status: response.data.status,
          position: response.data.position
        };
      } else {
        throw new Error(response.message || '启动面试失败');
      }
    } catch (error: any) {
      console.error('❌ InterviewSessionManager: Atomic API call failed:', error);

      // 处理特定错误类型
      if (error.message && error.message.includes('正式面试次数不足')) {
        throw new Error(`INSUFFICIENT_CREDITS:${error.message}`);
      }

      throw error;
    }
  }

  /**
   * 阶段4：激活 - 进入面试页面时
   */
  public async activate(): Promise<void> {
    console.log('⚡ InterviewSessionManager: Activating interview session');
    
    try {
      this.updatePhase('active');
      this.state.startTime = Date.now();
      
      // 激活所有已准备好的资源（扣费已在start阶段完成）
      await Promise.all([
        this.webSocketManager.activateSession(this.state.sessionId!),
        this.audioManager.activateProcessors()
      ]);
      
      console.log('✅ InterviewSessionManager: Session activated successfully');
      this.syncStateToStore();
    } catch (error) {
      console.error('❌ InterviewSessionManager: Activation failed:', error);
      this.updatePhase('error');
      throw error;
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 InterviewSessionManager: Cleaning up resources');
    
    await Promise.allSettled([
      this.webSocketManager.cleanup(),
      this.audioManager.cleanup(),
      this.creditsManager.cleanup()
    ]);
    
    this.resetState();
    console.log('✅ InterviewSessionManager: Cleanup completed');
  }

  // 私有方法
  private generateSessionId(config: InterviewConfig): string {
    const type: InterviewType = config.interviewType === 'mock' ? 'mock' : 'formal';
    return generateSessionId(config.selectedPositionId, type);
  }

  private validateReadiness(): void {
    const { webSocket, audio, credits } = this.state.resources;
    
    if (webSocket.status !== 'ready') {
      throw new Error('WebSocket连接未就绪');
    }
    if (audio.status !== 'ready') {
      throw new Error('音频系统未就绪');
    }
    if (credits.status !== 'ready') {
      throw new Error('权限验证未完成');
    }
  }

  private updatePhase(phase: SessionPhase): void {
    this.state.phase = phase;
    this.syncStateToStore();
  }

  private updateResourceProgress(resource: keyof SessionState['resources'], progress: number): void {
    this.state.progress[resource] = progress;
    this.state.resources[resource].status = progress === 100 ? 'ready' : 'preparing';
    this.state.resources[resource].lastUpdated = Date.now();
    this.updateOverallProgress();
    this.syncStateToStore();
  }

  private updateOverallProgress(): void {
    const { webSocket, audio, credits } = this.state.progress;
    this.state.progress.overall = Math.round((webSocket + audio + credits) / 3);
  }

  private resetState(): void {
    this.state = {
      phase: 'idle',
      progress: {
        webSocket: 0,
        audio: 0,
        credits: 0,
        overall: 0
      },
      resources: {
        webSocket: { status: 'idle', lastUpdated: Date.now() },
        audio: { status: 'idle', lastUpdated: Date.now() },
        credits: { status: 'idle', lastUpdated: Date.now() }
      }
    };
    this.syncStateToStore();
  }

  private syncStateToStore(): void {
    const store = useInterviewSessionStore.getState();
    store.updateSessionState(this.state);
  }

  // 公共访问器
  public getState(): SessionState {
    return { ...this.state };
  }

  public getSessionId(): string | undefined {
    return this.state.sessionId;
  }

  public getWebSocketManager(): WebSocketManager {
    return this.webSocketManager;
  }

  public getAudioManager(): AudioManager {
    return this.audioManager;
  }

  /**
   * 🔧 修复：处理WebSocket消息发送事件
   */
  private handleWebSocketSendMessage(event: CustomEvent): void {
    const { message, callback } = event.detail;

    try {
      if (this.state.sessionId && this.webSocketManager) {
        // 使用活跃的WebSocket管理器发送消息
        this.webSocketManager.sendMessage(this.state.sessionId, message);
        console.log('📤 WebSocket: Message sent via InterviewSessionManager:', message);

        // 通知发送成功
        if (callback) callback(true);
        return;
      }
    } catch (error) {
      console.error('❌ WebSocket: Error sending message via InterviewSessionManager:', error);
    }

    // 通知发送失败
    if (callback) callback(false);
  }
}
