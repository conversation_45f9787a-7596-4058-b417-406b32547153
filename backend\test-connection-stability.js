#!/usr/bin/env node

/**
 * 连接稳定性测试脚本
 * 用于验证WebSocket连接修复的效果
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

// 配置
const WS_URL = 'ws://localhost:3001';
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const TEST_USER_ID = 'test-user-123';
const TEST_SESSIONS = 5; // 测试5个连续会话

// 生成测试JWT
function generateTestToken(userId) {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '1h' });
}

// 生成会话ID
function generateSessionId(index) {
  return `session_test_${Date.now()}_${index}`;
}

// 测试单个会话
function testSession(sessionId, token) {
  return new Promise((resolve, reject) => {
    const wsUrl = `${WS_URL}/api/ws/interview/${sessionId}?token=${token}&mode=mock`;
    console.log(`🔗 Testing session: ${sessionId}`);
    
    const ws = new WebSocket(wsUrl);
    let connected = false;
    let messageReceived = false;
    
    const timeout = setTimeout(() => {
      if (!connected) {
        ws.close();
        reject(new Error(`Connection timeout for ${sessionId}`));
      }
    }, 5000);
    
    ws.on('open', () => {
      connected = true;
      console.log(`✅ Connected to session: ${sessionId}`);
      
      // 发送测试消息
      ws.send(JSON.stringify({
        type: 'test_message',
        text: 'Hello from test script'
      }));
    });
    
    ws.on('message', (data) => {
      messageReceived = true;
      console.log(`📨 Received message from ${sessionId}:`, data.toString().substring(0, 100));
    });
    
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      console.log(`🔌 Session ${sessionId} closed with code: ${code}, reason: ${reason}`);
      
      if (connected) {
        resolve({
          sessionId,
          success: true,
          connected,
          messageReceived,
          closeCode: code
        });
      } else {
        reject(new Error(`Failed to connect to ${sessionId}`));
      }
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`❌ Error in session ${sessionId}:`, error.message);
      reject(error);
    });
    
    // 模拟面试结束
    setTimeout(() => {
      if (connected) {
        ws.close(1000, 'Test completed');
      }
    }, 2000);
  });
}

// 连续会话测试
async function testContinuousSessions() {
  console.log(`🚀 Starting continuous session test with ${TEST_SESSIONS} sessions`);
  
  const token = generateTestToken(TEST_USER_ID);
  const results = [];
  
  for (let i = 1; i <= TEST_SESSIONS; i++) {
    const sessionId = generateSessionId(i);
    
    try {
      console.log(`\n--- Testing Session ${i}/${TEST_SESSIONS} ---`);
      const result = await testSession(sessionId, token);
      results.push(result);
      
      // 短暂延迟，模拟用户快速连续操作
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ Session ${i} failed:`, error.message);
      results.push({
        sessionId,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

// 分析测试结果
function analyzeResults(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log('\n📊 Test Results Summary:');
  console.log(`Total sessions: ${results.length}`);
  console.log(`Successful: ${successful.length} (${(successful.length/results.length*100).toFixed(1)}%)`);
  console.log(`Failed: ${failed.length} (${(failed.length/results.length*100).toFixed(1)}%)`);
  
  if (failed.length > 0) {
    console.log('\n❌ Failed sessions:');
    failed.forEach(f => {
      console.log(`  - ${f.sessionId}: ${f.error}`);
    });
  }
  
  // 计算稳定性指标
  const stabilityRate = successful.length / results.length;
  const targetRate = 0.95; // 目标95%稳定性
  
  console.log(`\n🎯 Stability Analysis:`);
  console.log(`Current stability rate: ${(stabilityRate * 100).toFixed(1)}%`);
  console.log(`Target stability rate: ${(targetRate * 100).toFixed(1)}%`);
  
  if (stabilityRate >= targetRate) {
    console.log('✅ PASS: Stability target achieved!');
    return true;
  } else {
    console.log('❌ FAIL: Stability target not met');
    return false;
  }
}

// 主函数
async function main() {
  try {
    console.log('🔧 WebSocket Connection Stability Test');
    console.log('=====================================');
    
    const results = await testContinuousSessions();
    const passed = analyzeResults(results);
    
    process.exit(passed ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test script error:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { testSession, testContinuousSessions, analyzeResults };
