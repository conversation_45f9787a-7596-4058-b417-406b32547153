// WebSocket资源管理器
import { InterviewConfig } from './InterviewSessionManager';
import useAuthStore from '../stores/authStore';
import { connectionTracker } from '../utils/ConnectionTracker';

export interface WebSocketConnection {
  ws: WebSocket;
  sessionId: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastActivity: number;
  retryCount: number;
}

export interface WebSocketPool {
  [sessionId: string]: WebSocketConnection;
}

/**
 * WebSocket资源管理器（重构版）
 * 负责WebSocket连接的智能管理，移除过度预连接，实现按需连接
 */
export class WebSocketManager {
  private connectionPool: WebSocketPool = {};
  private maxRetries = 3;
  private retryDelay = 1000;
  private connectionReuse = true; // 启用连接复用
  private systemCheckCache: { [key: string]: any } = {}; // 系统检查缓存
  private systemCheckCacheExpiry = 5 * 60 * 1000; // 5分钟缓存
  private messageListeners: Map<string, ((message: any) => void)[]> = new Map();

  /**
   * 简化初始化 - 移除预热逻辑，改为空操作
   */
  public async warmUp(): Promise<void> {
    console.log('🔧 WebSocketManager: Warm-up disabled (using on-demand connections)');

    // 不进行任何预热操作，连接将在需要时创建
    console.log('✅ WebSocketManager: Ready (no warm-up needed)');
  }

  /**
   * 创建会话 - 智能分配WebSocket连接（重构版）
   */
  public async createSession(sessionId: string, config: InterviewConfig, mode: 'live' | 'mock' = 'live', positionId?: string): Promise<void> {
    console.log(`🔧 WebSocketManager: Creating smart session ${sessionId} with position ${positionId || 'none'}`);

    try {
      // 检查是否已有可复用的连接
      const existingConnection = this.connectionPool[sessionId];
      if (existingConnection && existingConnection.ws.readyState === WebSocket.OPEN) {
        console.log('♻️ WebSocketManager: Reusing existing session connection');
        existingConnection.lastActivity = Date.now();
        return;
      }

      // 创建智能连接（使用缓存的系统检查）
      const ws = await this.createSmartConnection(sessionId, mode, positionId);

      // 配置会话特定的WebSocket
      await this.configureSessionWebSocket(ws, sessionId, config);

      // 添加到连接池
      this.connectionPool[sessionId] = {
        ws,
        sessionId,
        status: 'connected',
        lastActivity: Date.now(),
        retryCount: 0
      };

      console.log(`✅ WebSocketManager: Smart session ${sessionId} created successfully`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to create session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 激活会话 - 开始实际的面试会话
   */
  public async activateSession(sessionId: string): Promise<void> {
    console.log(`⚡ WebSocketManager: Activating session ${sessionId}`);
    
    const connection = this.connectionPool[sessionId];
    if (!connection) {
      throw new Error(`Session ${sessionId} not found in pool`);
    }
    
    try {
      // 发送激活消息
      const activationMessage = {
        type: 'activate_session',
        sessionId,
        timestamp: Date.now()
      };
      
      connection.ws.send(JSON.stringify(activationMessage));
      connection.lastActivity = Date.now();
      
      console.log(`✅ WebSocketManager: Session ${sessionId} activated`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to activate session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 获取会话连接
   */
  public getSessionConnection(sessionId: string): WebSocketConnection | undefined {
    return this.connectionPool[sessionId];
  }

  /**
   * 发送消息
   */
  public sendMessage(sessionId: string, message: any): void {
    const connection = this.connectionPool[sessionId];
    if (!connection || connection.status !== 'connected') {
      throw new Error(`Session ${sessionId} is not connected`);
    }

    // 🔥 添加详细的WebSocket状态检查
    console.log(`🔍 WebSocketManager: Sending message to ${sessionId}`);
    console.log(`🔍 WebSocket readyState: ${connection.ws.readyState} (0=CONNECTING, 1=OPEN, 2=CLOSING, 3=CLOSED)`);
    console.log(`🔍 Connection status: ${connection.status}`);
    console.log(`🔍 Message type: ${message.type}`);

    if (connection.ws.readyState !== WebSocket.OPEN) {
      throw new Error(`WebSocket is not open (readyState: ${connection.ws.readyState})`);
    }

    try {
      const messageStr = JSON.stringify(message);
      console.log(`🔍 Sending message: ${messageStr.substring(0, 200)}...`);
      connection.ws.send(messageStr);
      connection.lastActivity = Date.now();
      console.log(`✅ Message sent successfully to ${sessionId}`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to send message to ${sessionId}:`, error);
      this.handleConnectionError(sessionId, error);
      throw error;
    }
  }

  public addMessageListener(sessionId: string, listener: (message: any) => void): void {
    if (!this.messageListeners.has(sessionId)) {
      this.messageListeners.set(sessionId, []);
    }
    this.messageListeners.get(sessionId)!.push(listener);
  }

  public removeMessageListener(sessionId: string, listener: (message: any) => void): void {
    const listeners = this.messageListeners.get(sessionId);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 清理资源（重构版）
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 WebSocketManager: Cleaning up resources');

    // 关闭所有会话连接
    Object.values(this.connectionPool).forEach(connection => {
      if (connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }
    });

    // 清理连接池和缓存
    this.connectionPool = {};
    this.systemCheckCache = {};

    console.log('✅ WebSocketManager: Cleanup completed');
  }

  // 私有方法

  /**
   * 创建智能连接（使用缓存的系统检查）
   */
  private async createSmartConnection(sessionId: string, mode: 'live' | 'mock' = 'live', positionId?: string): Promise<WebSocket> {
    console.log(`🧠 WebSocketManager: Creating smart connection for ${sessionId}`);

    const wsUrl = this.buildWebSocketUrl(sessionId, mode, positionId);

    // 🔍 追踪连接创建
    connectionTracker.trackConnection(
      sessionId,
      'websocket',
      'WebSocketManager.createSmartConnection',
      { url: wsUrl, timestamp: Date.now() }
    );

    const ws = new WebSocket(wsUrl);

    try {
      await this.waitForConnection(ws, sessionId);
      console.log(`✅ WebSocketManager: Smart connection established for ${sessionId}`);
      connectionTracker.updateConnectionStatus(sessionId, 'connected');
      return ws;
    } catch (error) {
      console.error(`❌ WebSocketManager: Smart connection failed for ${sessionId}:`, error);
      connectionTracker.updateConnectionStatus(sessionId, 'error', { error: error.toString() });
      throw error;
    }
  }

  /**
   * 缓存系统检查结果
   */
  private async cacheSystemChecks(): Promise<void> {
    const cacheKey = 'system_checks';
    const now = Date.now();

    // 检查缓存是否有效
    if (this.systemCheckCache[cacheKey] &&
        (now - this.systemCheckCache[cacheKey].timestamp) < this.systemCheckCacheExpiry) {
      console.log('📋 WebSocketManager: System checks already cached');
      return;
    }

    console.log('📋 WebSocketManager: Caching system checks...');

    // 这里可以添加实际的系统检查逻辑
    // 目前只是模拟缓存
    this.systemCheckCache[cacheKey] = {
      timestamp: now,
      checks: {
        ffmpeg: 'available',
        asr: 'ready',
        network: 'connected'
      }
    };

    console.log('✅ WebSocketManager: System checks cached successfully');
  }

  private async createNewConnection(sessionId: string): Promise<WebSocket> {
    const wsUrl = this.buildWebSocketUrl(sessionId);
    const ws = new WebSocket(wsUrl);
    
    await this.waitForConnection(ws, sessionId);
    return ws;
  }

  private async configureSessionWebSocket(ws: WebSocket, sessionId: string, config: InterviewConfig): Promise<void> {
    // 设置消息处理器
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      // 调用所有监听器
      const listeners = this.messageListeners.get(sessionId) || [];
      listeners.forEach(listener => listener(data));
      // 原有消息处理逻辑
      this.handleMessage(sessionId, event);
    };
    
    // 设置错误处理器
    ws.onerror = (error) => {
      this.handleConnectionError(sessionId, error);
    };
    
    // 设置关闭处理器
    ws.onclose = (event) => {
      this.handleConnectionClose(sessionId, event);
    };
    
    // 发送初始化配置
    const initMessage = {
      type: 'initialize_session',
      sessionId,
      config: {
        interviewType: config.interviewType,
        language: config.interviewLanguage,
        answerStyle: config.answerStyle
      },
      timestamp: Date.now()
    };
    
    ws.send(JSON.stringify(initMessage));
  }

  private buildWebSocketUrl(sessionId: string, mode: 'live' | 'mock' = 'live', positionId?: string): string {
    // 正确获取token - 从AuthStore获取而不是直接从localStorage
    const token = useAuthStore.getState().token;

    // 使用更健壮的URL构建逻辑
    const baseUrl = this.getWsBaseUrl();

    // 构建URL参数
    const params = new URLSearchParams({
      token: token || '',
      mode: mode
    });

    if (positionId) {
      params.append('positionId', positionId);
    }

    const url = `${baseUrl}/api/ws/interview/${sessionId}?${params.toString()}`;
    console.log('🔐 WebSocketManager: Building URL with token:', token ? `present (${token.substring(0, 20)}...)` : 'missing');
    console.log('🔐 WebSocketManager: Full URL:', `${baseUrl}/api/ws/interview/${sessionId}?token=${token ? token.substring(0, 20) + '...' : 'null'}&mode=${mode}&positionId=${positionId || 'none'}`);
    return url;
  }

  /**
   * 获取正确的 WebSocket 服务基础 URL
   */
  private getWsBaseUrl(): string {
    // 生产环境的判断：优先检查 hostname 是否为生产域名
    if (window.location.hostname === 'mianshijun.xyz' || import.meta.env.PROD) {
      const protocol = 'wss:';
      const host = window.location.host; // 这将是 'mianshijun.xyz'
      return `${protocol}//${host}`;
    }

    // 默认返回开发环境地址
    return 'ws://localhost:3000';
  }

  private waitForConnection(ws: WebSocket, sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000);

      ws.onopen = () => {
        clearTimeout(timeout);
        console.log(`🔗 WebSocketManager: Connection opened for ${sessionId}, triggering connection change event`);

        // 触发连接状态变化事件
        window.dispatchEvent(new CustomEvent('websocket-connection-change', {
          detail: { sessionId, connected: true }
        }));

        resolve();
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(error);
      };
    });
  }

  private handleMessage(sessionId: string, event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      console.log(`📨 WebSocketManager: Message received for ${sessionId}:`, data);
      
      // 更新连接活动时间
      const connection = this.connectionPool[sessionId];
      if (connection) {
        connection.lastActivity = Date.now();
      }
      
      // 触发全局事件，让其他组件处理消息
      window.dispatchEvent(new CustomEvent('websocket-message', {
        detail: { sessionId, data }
      }));
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to parse message for ${sessionId}:`, error);
    }
  }

  // 🔥 修复版本：添加状态轮询的错误处理
  private async handleConnectionError(sessionId: string, error: any): Promise<void> {
    console.error(`❌ WebSocketManager: Connection error for ${sessionId}:`, error);

    const connection = this.connectionPool[sessionId];
    if (connection) {
      connection.status = 'error';
      connection.retryCount++;

      // 🔥 核心修复：重连前先轮询后端状态
      if (connection.retryCount <= this.maxRetries) {
        const status = await this.checkSessionStatus(sessionId);
        if (status === 'completed' || status === 'pending_cleanup') {
          // 放弃重连，提示用户开始新面试
          console.warn(`⚠️ Session ${sessionId} is ${status}, stopping retry attempts`);
          this.notifyUser('面试已结束，请开始新的面试');
          return;
        }

        // 指数退避重试
        const delay = this.retryDelay * Math.pow(2, connection.retryCount - 1); // 1s, 2s, 4s
        setTimeout(() => {
          this.retryConnection(sessionId);
        }, delay);
      } else {
        // 超过最大重试次数，提示用户
        this.notifyUser('连接失败，请重新开始面试');
      }
    }
  }

  private handleConnectionClose(sessionId: string, event: CloseEvent): void {
    console.log(`🔌 WebSocketManager: Connection closed for ${sessionId}:`, event);
    
    const connection = this.connectionPool[sessionId];
    if (connection) {
      connection.status = 'disconnected';
    }
  }

  // 🔥 修复版本：重连成功后重置重试计数
  private async retryConnection(sessionId: string): Promise<void> {
    console.log(`🔄 WebSocketManager: Retrying connection for ${sessionId}`);

    try {
      const connection = this.connectionPool[sessionId];
      if (!connection) return;

      // 关闭旧连接
      if (connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }

      // 创建新的智能连接
      const newWs = await this.createSmartConnection(sessionId);
      connection.ws = newWs;
      connection.status = 'connected';
      connection.lastActivity = Date.now();

      // 🔥 成功后重置重试计数
      connection.retryCount = 0;

      console.log(`✅ WebSocketManager: Retry successful for ${sessionId}`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Retry failed for ${sessionId}:`, error);
      await this.handleConnectionError(sessionId, error);
    }
  }

  /**
   * 🔥 新方法：检查会话状态 - 用于重连前的状态轮询
   */
  private async checkSessionStatus(sessionId: string): Promise<'active' | 'pending_cleanup' | 'completed' | 'not_found'> {
    try {
      const authStore = useAuthStore.getState();
      const token = authStore.token;

      if (!token) {
        console.error('❌ No auth token available for session status check');
        return 'not_found';
      }

      const response = await fetch(`/api/interviews/check-session/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`❌ Session status check failed: ${response.status}`);
        return 'not_found';
      }

      const data = await response.json();
      console.log(`🔍 Session ${sessionId} status check result:`, data);

      return data.status || 'not_found';
    } catch (error) {
      console.error(`❌ Error checking session status for ${sessionId}:`, error);
      return 'active'; // 保守处理：发生错误时允许重连
    }
  }

  /**
   * 🔥 新方法：用户通知 - 使用现有UI组件显示提示
   */
  private notifyUser(message: string): void {
    // 这里可以集成现有的通知系统，比如toast或者聊天界面的系统消息
    console.log(`📢 User notification: ${message}`);

    // 可以通过事件系统通知UI组件显示消息
    // 例如：window.dispatchEvent(new CustomEvent('user-notification', { detail: message }));

    // 或者直接在控制台显示，让用户看到
    if (typeof window !== 'undefined') {
      // 简单的alert作为临时解决方案，实际应该使用更好的UI组件
      setTimeout(() => {
        alert(message);
      }, 100);
    }
  }

  /**
   * 简化的连接健康检查（仅清理死连接）
   */
  private cleanupDeadConnections(): void {
    Object.entries(this.connectionPool).forEach(([sessionId, connection]) => {
      if (connection.ws.readyState !== WebSocket.OPEN) {
        console.log(`🔌 WebSocketManager: Removing dead connection ${sessionId}`);
        delete this.connectionPool[sessionId];
      }
    });
  }
}
