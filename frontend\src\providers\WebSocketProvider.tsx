// WebSocket Provider - 基于ES模块单例的现代化架构实现
import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import * as webSocketManager from '../services/webSocketManager';
import { useFeatureFlags } from './FeatureFlagProvider';
import type { ConnectionState, ConnectionConfig, WebSocketMessage, ConnectionMetrics } from '../services/webSocketManager';

interface WebSocketContextValue {
  // 连接管理
  connect: (sessionId: string, config: ConnectionConfig) => Promise<void>;
  disconnect: () => void;
  sendMessage: (message: WebSocketMessage) => void;

  // 状态查询
  connectionState: ConnectionState;
  isConnected: boolean;
  currentSessionId: string | null;

  // 指标和监控
  metrics: ConnectionMetrics;

  // 事件监听
  addEventListener: (eventType: string, callback: (data: any) => void) => () => void;
}

// 创建Context
const WebSocketContext = createContext<WebSocketContextValue | null>(null);

interface WebSocketProviderProps {
  children: ReactNode;
}

/**
 * WebSocket Provider - 基于ES模块单例的现代化实现
 * 实现"单例即服务"架构模式
 */
export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { isEnabled } = useFeatureFlags();

  // 本地状态管理
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected');
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<ConnectionMetrics>(webSocketManager.getMetrics());
  const [isConnected, setIsConnected] = useState<boolean>(false);

  // 更新状态的函数
  const updateState = useCallback((eventData?: { sessionId?: string; connected?: boolean }) => {
    // 如果有事件数据，优先使用事件数据中的连接状态
    if (eventData) {
      console.log('🔍 WebSocketProvider: Using event data:', eventData);
      setIsConnected(!!eventData.connected);
      if (eventData.sessionId) {
        setCurrentSessionId(eventData.sessionId);
      }
      // 对于connectionState，我们需要构造一个兼容的对象
      const eventConnState = eventData.connected ? 'connected' : 'disconnected';
      setConnectionState(eventConnState);
    } else {
      // 回退到查询管理器（用于初始化等情况）
      console.log('🔍 WebSocketProvider: Fallback to manager query');
      const connState = webSocketManager.getConnectionState();
      setConnectionState(connState);
      setCurrentSessionId(webSocketManager.getCurrentSessionId());
      setIsConnected(webSocketManager.isConnected());
    }

    // 指标总是从管理器获取
    setMetrics(webSocketManager.getMetrics());
  }, []);

  // 监听WebSocket事件
  useEffect(() => {
    console.log('🚀 WebSocketProvider: Initializing ES module WebSocket manager');

    // 监听连接状态变化
    const handleConnectionChange = (event: CustomEvent) => {
      console.log('🔄 WebSocketProvider: Connection state changed:', event.detail);
      updateState(event.detail); // 传递事件数据
    };

    // 监听WebSocket错误
    const handleWebSocketError = (event: CustomEvent) => {
      console.error('🚨 WebSocketProvider: WebSocket error:', event.detail);
      updateState();
    };

    // 监听消息（用于调试）
    const handleMessage = (event: CustomEvent) => {
      if (isEnabled('enable-websocket-debug-logs')) {
        console.log('📨 WebSocketProvider: Message received:', event.detail);
      }
    };

    // 添加事件监听器
    window.addEventListener('websocket-connection-change', handleConnectionChange as EventListener);
    window.addEventListener('websocket-error', handleWebSocketError as EventListener);
    window.addEventListener('websocket-message', handleMessage as EventListener);

    // 初始化状态
    updateState();

    // 定期更新指标（每5秒）
    const metricsInterval = setInterval(() => {
      setMetrics(webSocketManager.getMetrics());
    }, 5000);

    // 清理函数
    return () => {
      console.log('🧹 WebSocketProvider: Cleaning up');
      window.removeEventListener('websocket-connection-change', handleConnectionChange as EventListener);
      window.removeEventListener('websocket-error', handleWebSocketError as EventListener);
      window.removeEventListener('websocket-message', handleMessage as EventListener);
      clearInterval(metricsInterval);
    };
  }, [updateState, isEnabled]);

  // 应用卸载时清理WebSocket资源
  useEffect(() => {
    return () => {
      webSocketManager.cleanup();
    };
  }, []);

  // Context值
  const contextValue: WebSocketContextValue = {
    // 连接管理
    connect: useCallback(async (sessionId: string, config: ConnectionConfig) => {
      try {
        await webSocketManager.connect(sessionId, config);
        updateState();
      } catch (error) {
        console.error('❌ WebSocketProvider: Connection failed:', error);
        updateState();
        throw error;
      }
    }, [updateState]),

    disconnect: useCallback(() => {
      webSocketManager.disconnect();
      updateState();
    }, [updateState]),

    sendMessage: useCallback((message: WebSocketMessage) => {
      // 🔧 修复：检查是否有活跃的WebSocket连接
      let messageSent = false;

      // 尝试通过全局事件发送消息
      if (typeof window !== 'undefined') {
        const sendEvent = new CustomEvent('websocket-send-message', {
          detail: { message, callback: (success: boolean) => { messageSent = success; } }
        });
        window.dispatchEvent(sendEvent);
      }

      // 如果没有通过活跃连接发送，使用旧管理器
      if (!messageSent) {
        webSocketManager.sendMessage(message);
      }

      // 发送消息后更新指标
      setTimeout(updateState, 100);
    }, [updateState]),

    // 状态查询
    connectionState,
    isConnected,
    currentSessionId,

    // 指标和监控
    metrics,

    // 事件监听
    addEventListener: webSocketManager.addEventListener
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

/**
 * useWebSocket Hook - 组件访问WebSocket服务的唯一入口
 * 实现"单例即服务"模式的依赖注入机制
 */
export const useWebSocket = (): WebSocketContextValue => {
  const context = useContext(WebSocketContext);

  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }

  return context;
};

// 导出类型
export type { WebSocketContextValue, ConnectionState, ConnectionConfig, WebSocketMessage, ConnectionMetrics };
