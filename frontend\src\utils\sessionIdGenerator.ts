/**
 * 统一的SessionId生成工具
 * 确保整个应用中sessionId格式的一致性
 */

export type InterviewType = 'mock' | 'formal';

/**
 * 生成标准格式的sessionId
 * 格式：session_{positionId}_{type}_{timestamp}
 * 
 * @param positionId 岗位ID
 * @param type 面试类型：'mock' | 'formal'
 * @returns 标准格式的sessionId
 */
export function generateSessionId(positionId: string, type: InterviewType): string {
  const timestamp = Date.now();
  return `session_${positionId}_${type}_${timestamp}`;
}

/**
 * 解析sessionId，提取其中的信息
 * 
 * @param sessionId 要解析的sessionId
 * @returns 解析结果或null（如果格式不正确）
 */
export function parseSessionId(sessionId: string): {
  positionId: string;
  type: InterviewType;
  timestamp: number;
} | null {
  const parts = sessionId.split('_');
  
  if (parts.length < 4 || parts[0] !== 'session') {
    return null;
  }
  
  const positionId = parts[1];
  const type = parts[2] as InterviewType;
  const timestamp = parseInt(parts[3], 10);
  
  if (!positionId || !['mock', 'formal'].includes(type) || isNaN(timestamp)) {
    return null;
  }
  
  return {
    positionId,
    type,
    timestamp
  };
}

/**
 * 验证sessionId格式是否正确
 * 
 * @param sessionId 要验证的sessionId
 * @returns 是否为有效格式
 */
export function isValidSessionId(sessionId: string): boolean {
  return parseSessionId(sessionId) !== null;
}

/**
 * 从sessionId中提取岗位ID
 * 
 * @param sessionId sessionId
 * @returns 岗位ID或null
 */
export function extractPositionId(sessionId: string): string | null {
  const parsed = parseSessionId(sessionId);
  return parsed ? parsed.positionId : null;
}

/**
 * 从sessionId中提取面试类型
 * 
 * @param sessionId sessionId
 * @returns 面试类型或null
 */
export function extractInterviewType(sessionId: string): InterviewType | null {
  const parsed = parseSessionId(sessionId);
  return parsed ? parsed.type : null;
}
