import { fetchWithAuth } from './apiService';

export interface UserInfo {
  id: string;
  email: string;
  name?: string;
  phoneNumber?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  hasSeenWelcome?: boolean;
}

/**
 * 获取当前用户信息
 */
export const getUserInfo = async (): Promise<UserInfo> => {
  const responseData = await fetchWithAuth<{success: boolean, data: UserInfo}>('/users/me');
  
  if (!responseData.success || !responseData.data) {
    throw new Error('用户信息获取失败');
  }
  
  return responseData.data;
};

/**
 * 标记用户已查看欢迎弹窗
 */
export const markWelcomeSeen = async (): Promise<void> => {
  const responseData = await fetchWithAuth<{success: boolean, message: string}>('/users/me/mark-welcome-seen', {
    method: 'POST'
  });
  
  if (!responseData.success) {
    throw new Error(responseData.message || '标记欢迎弹窗失败');
  }
};

/**
 * 用户相关API
 */
export const userApi = {
  getUserInfo,
  markWelcomeSeen
};
