// 音频处理器
import { EventEmitter } from 'events';
import { AudioFormat, AudioBuffer, PcmAudioBuffer, AudioSegmentInfo } from '../../../types/audio.js';
import { FFmpegProcessor } from './ffmpegProcessor.js';
import { ASRServiceManager } from '../asr/asrServiceManager.js';
import { ASRResult } from '../../../types/asr.js';
import { DashScopeProvider, StreamingCallbacks } from '../asr/dashscopeProvider.js';
import prisma from '../../../lib/prisma.js';
import { TextSimilarityDetector, SimilarityResult } from '../../../utils/textSimilarity.js';
import { LRUCache } from 'lru-cache';
import { systemMonitor } from '../../../monitoring/SystemMonitor.js';

// 语音缓冲接口
interface SpeechBuffer {
  transcriptions: string[];
  lastTranscriptionTime: number;
  timeoutId?: NodeJS.Timeout;
  lastTranscription?: string;
  isProcessing?: boolean;
  lastProcessedText?: string;
}

export class AudioProcessor extends EventEmitter {
  private ffmpegProcessor: FFmpegProcessor;
  private asrServiceManager: ASRServiceManager;
  private dashscopeProvider: DashScopeProvider; // 🔥 保留作为备用，优先使用会话级连接
  private audioBuffers: Map<string, AudioBuffer> = new Map();
  private pcmAudioBuffers: Map<string, PcmAudioBuffer> = new Map();
  private speechBuffers: Map<string, SpeechBuffer> = new Map();

  // 🔥 修复：为每个会话创建独立的DashScope Provider实例
  private sessionProviders: Map<string, DashScopeProvider> = new Map();

  // 🔥 流式会话管理
  private streamingSessions: Map<string, boolean> = new Map();
  private currentBubbles: Map<string, string> = new Map(); // sessionId -> bubbleId

  // 增强的去重系统
  private textSimilarityDetector: TextSimilarityDetector;
  private textCache: LRUCache<string, { text: string; timestamp: number; fingerprint: string }>;
  private slidingWindowCache: Map<string, Array<{ text: string; timestamp: number; similarity: number }>> = new Map();

  private readonly BUFFER_SIZE_THRESHOLD = 50000; // 50KB
  private readonly SPEECH_PAUSE_TIMEOUT = 5000; // 5秒 - 优化：容忍更长的自然停顿
  private readonly TEXT_CACHE_SIZE = 1000; // 文本缓存大小
  private readonly SLIDING_WINDOW_SIZE = 5; // 滑动窗口大小（秒）
  private readonly DUPLICATE_THRESHOLD = 0.85; // 重复检测阈值

  constructor(asrServiceManager: ASRServiceManager) {
    super();
    this.ffmpegProcessor = new FFmpegProcessor();
    this.asrServiceManager = asrServiceManager;
    this.dashscopeProvider = new DashScopeProvider(); // 备用提供商

    // 初始化文本相似度检测器
    this.textSimilarityDetector = new TextSimilarityDetector({
      editDistanceWeight: 0.6,
      cosineWeight: 0.4,
      duplicateThreshold: this.DUPLICATE_THRESHOLD,
      minTextLength: 3
    });
    
    // 初始化LRU缓存
    this.textCache = new LRUCache({
      max: this.TEXT_CACHE_SIZE,
      ttl: 1000 * 60 * 5 // 5分钟TTL
    });

    // 启动定期清理任务
    this.startPeriodicCleanup();
  }

  /**
   * 🔥 修复：获取或创建会话专用的DashScope提供商
   */
  private getDashScopeProvider(sessionId: string): DashScopeProvider {
    if (!this.sessionProviders.has(sessionId)) {
      console.log(`[${sessionId}] Creating dedicated DashScope provider`);
      const provider = new DashScopeProvider();
      this.sessionProviders.set(sessionId, provider);
      console.log(`[${sessionId}] ✅ DashScope provider created successfully`);
    }
    return this.sessionProviders.get(sessionId)!;
  }

  /**
   * 🔥 修复：启动流式识别会话 - 使用会话专用provider
   */
  async startStreamingSession(sessionId: string): Promise<void> {
    if (this.streamingSessions.get(sessionId)) {
      console.log(`[${sessionId}] Streaming session already active`);
      return;
    }

    console.log(`[${sessionId}] Starting streaming ASR session with dedicated connection...`);

    // 🔥 修复：使用会话专用的DashScope提供商
    const sessionProvider = this.getDashScopeProvider(sessionId);

    const callbacks: StreamingCallbacks = {
      onPartialResult: (text: string, bubbleId: string) => {
        console.log(`[${sessionId}] 📝 Partial result for bubble ${bubbleId}: "${text}"`);
        this.emit('transcription_partial', {
          sessionId,
          text,
          bubbleId,
          timestamp: Date.now()
        });
      },
      onFinalResult: (text: string, bubbleId: string) => {
        console.log(`[${sessionId}] 🎯 Final result for bubble ${bubbleId}: "${text}"`);
        this.emit('transcription_final', {
          sessionId,
          text,
          bubbleId,
          timestamp: Date.now()
        });

        // 🔥 触发LLM处理
        console.log(`[${sessionId}] 🤖 Triggering LLM with text: "${text}"`);
        this.emit('llm_trigger', {
          sessionId,
          text
        });
      },
      onError: (error) => {
        console.error(`[${sessionId}] Streaming ASR error:`, error);
        this.emit('asr_error', { sessionId, error });
      }
    };

    try {
      await sessionProvider.startStreamingSession(callbacks);
      this.streamingSessions.set(sessionId, true);
      console.log(`[${sessionId}] ✅ Streaming session started successfully`);
    } catch (error) {
      console.error(`[${sessionId}] ❌ Failed to start streaming session:`, {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        type: error instanceof Error ? error.constructor.name : typeof error
      });
      throw error;
    }
  }

  /**
   * 🔥 修复：停止流式识别会话 - 使用会话专用provider
   */
  async stopStreamingSession(sessionId: string): Promise<void> {
    if (!this.streamingSessions.get(sessionId)) {
      console.log(`[${sessionId}] No active streaming session to stop`);
      return;
    }

    console.log(`[${sessionId}] Stopping streaming ASR session...`);

    try {
      // 🔥 修复：使用会话专用的DashScope提供商
      const sessionProvider = this.sessionProviders.get(sessionId);
      if (sessionProvider) {
        await sessionProvider.stopStreamingSession();
        await sessionProvider.destroy();
        this.sessionProviders.delete(sessionId);
      }

      this.streamingSessions.delete(sessionId);
      this.currentBubbles.delete(sessionId);
      console.log(`[${sessionId}] ✅ Streaming session stopped successfully`);
    } catch (error) {
      console.error(`[${sessionId}] ❌ Failed to stop streaming session:`, error);
    }
  }

  /**
   * 🔥 修复：处理流式音频数据 - 改进错误处理
   */
  async processStreamingAudio(sessionId: string, audioBuffer: Buffer): Promise<void> {
    if (!this.streamingSessions.get(sessionId)) {
      console.log(`[${sessionId}] No active streaming session, starting one...`);
      try {
        await this.startStreamingSession(sessionId);
      } catch (error) {
        console.error(`[${sessionId}] ❌ Failed to start streaming session:`, {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          type: error instanceof Error ? error.constructor.name : typeof error
        });
        // 🔥 修复：流式会话启动失败时，标记为失败状态，避免无限重试
        this.streamingSessions.set(sessionId, false);
        throw error;
      }
    }

    // 检查流式会话是否真的可用
    if (this.streamingSessions.get(sessionId) === false) {
      throw new Error(`[${sessionId}] Streaming session is in failed state`);
    }

    // 🔥 修复：使用会话专用的DashScope提供商发送音频数据
    console.log(`[${sessionId}] 🎵 Processing streaming audio: ${audioBuffer.length} bytes`);

    try {
      const sessionProvider = this.sessionProviders.get(sessionId);
      if (!sessionProvider) {
        throw new Error(`No DashScope provider found for session ${sessionId}`);
      }

      sessionProvider.addAudioData(audioBuffer);
      console.log(`[${sessionId}] ✅ Audio sent to DashScope stream`);

      // 🔥 记录音频处理成功
      systemMonitor.recordAudioProcessing(audioBuffer.length, true);
    } catch (error) {
      console.error(`[${sessionId}] ❌ Failed to send audio to DashScope:`, error);

      // 🔥 记录音频处理失败
      systemMonitor.recordAudioProcessing(audioBuffer.length, false);
      throw error;
    }
  }

  /**
   * 处理智能音频段（流式模式）
   */
  async processSmartAudioSegment(sessionId: string, audioBuffer: Buffer, segmentInfo?: AudioSegmentInfo): Promise<void> {
    console.log(`[${sessionId}] Processing smart audio segment: ${audioBuffer.length} bytes`);

    if (segmentInfo) {
      console.log(`[${sessionId}] Segment info:`, {
        id: segmentInfo.id,
        duration: segmentInfo.duration,
        confidence: segmentInfo.confidence,
        segmentType: segmentInfo.segmentType,
        triggerReason: segmentInfo.triggerReason
      });
    }

    // 🔥 使用流式处理模式
    try {
      await this.processStreamingAudio(sessionId, audioBuffer);
    } catch (error) {
      console.error(`[${sessionId}] Streaming audio processing error:`, error);
      // 降级到传统ASR处理
      console.log(`[${sessionId}] Falling back to traditional ASR processing...`);
      try {
        const result = await this.asrServiceManager.recognize(audioBuffer);
        if (result && result.text.trim()) {
          console.log(`[${sessionId}] Fallback ASR result: ${result.text}`);
          // 发送传统ASR结果
          this.emit('transcription_final', {
            sessionId,
            text: result.text,
            bubbleId: `fallback-${Date.now()}`,
            timestamp: Date.now()
          });
        }
      } catch (fallbackError) {
        console.error(`[${sessionId}] Fallback ASR also failed:`, fallbackError);
      }
    }
  }

  /**
   * 处理PCM音频块（流式模式）
   */
  async processPCMAudioChunk(sessionId: string, pcmBuffer: Buffer, options: { sampleRate: number; channels: number }): Promise<void> {
    console.log(`[${sessionId}] Processing PCM audio chunk: ${pcmBuffer.length} bytes`);

    // 🔥 直接发送到流式处理，无需缓冲
    try {
      await this.processStreamingAudio(sessionId, pcmBuffer);
    } catch (error) {
      console.error(`[${sessionId}] Streaming PCM processing error:`, {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        type: error instanceof Error ? error.constructor.name : typeof error
      });

      // 降级到传统缓冲模式
      console.log(`[${sessionId}] Falling back to buffered PCM processing...`);

      // 添加到PCM缓冲区
      let buffer = this.pcmAudioBuffers.get(sessionId);
      if (!buffer) {
        buffer = {
          chunks: [],
          totalSize: 0,
          lastChunkTime: Date.now(),
          isProcessing: false
        };
        this.pcmAudioBuffers.set(sessionId, buffer);
      }

      buffer.chunks.push(pcmBuffer);
      buffer.totalSize += pcmBuffer.length;
      buffer.lastChunkTime = Date.now();

      console.log(`[${sessionId}] PCM buffer updated: ${buffer.totalSize} bytes (${buffer.chunks.length} chunks)`);

      // 🔧 优化：减少缓冲区大小，提高响应速度
      const MAX_BUFFER_BEFORE_FORCED_SEND = options.sampleRate * 2 * 1; // 1秒的音频数据 (约32KB)
      if (buffer.totalSize >= MAX_BUFFER_BEFORE_FORCED_SEND && !buffer.isProcessing) {
        console.log(`[${sessionId}] PCM buffer size reached threshold (${buffer.totalSize} bytes), processing ASR...`);
        await this.processAndSendPCMAudioForASR(sessionId);
      }
    }
  }

  /**
   * 处理其他格式音频块
   */
  async processAudioChunk(sessionId: string, audioBuffer: Buffer, format: string): Promise<void> {
    console.log(`[${sessionId}] Processing ${format} audio chunk: ${audioBuffer.length} bytes`);
    
    try {
      // 使用FFmpeg转换为PCM
      const pcmBuffer = await this.ffmpegProcessor.convertToPCM(audioBuffer, format);
      console.log(`[${sessionId}] Converted ${format} to PCM: ${pcmBuffer.length} bytes`);
      
      // 处理转换后的PCM数据
      await this.processPCMAudioChunk(sessionId, pcmBuffer, { sampleRate: 16000, channels: 1 });
    } catch (error) {
      console.error(`[${sessionId}] Error converting ${format} audio:`, error);
      throw error;
    }
  }

  /**
   * 处理原始音频数据
   */
  async processRawAudioData(sessionId: string, audioData: Buffer): Promise<void> {
    console.log(`[${sessionId}] Processing raw audio data: ${audioData.length} bytes`);

    // 检测音频格式
    const format = this.detectAudioFormat(audioData);
    console.log(`[${sessionId}] Detected audio format: ${format}`);

    // 根据格式处理
    if (format === AudioFormat.PCM) {
      console.log(`[${sessionId}] Processing as PCM audio directly`);
      await this.processPCMAudioChunk(sessionId, audioData, { sampleRate: 16000, channels: 1 });
    } else if (format === AudioFormat.UNKNOWN) {
      // 对于未知格式，尝试当作PCM处理
      console.log(`[${sessionId}] Unknown format, trying as PCM audio`);
      await this.processPCMAudioChunk(sessionId, audioData, { sampleRate: 16000, channels: 1 });
    } else {
      console.log(`[${sessionId}] Processing as ${format} audio with FFmpeg conversion`);
      await this.processAudioChunk(sessionId, audioData, format);
    }
  }

  /**
   * 检测音频格式
   */
  private detectAudioFormat(buffer: Buffer): string {
    if (!buffer || buffer.length < 12) {
      console.log(`⚠️ Audio buffer too small for format detection: ${buffer?.length || 0} bytes`);
      return AudioFormat.UNKNOWN;
    }

    const header = buffer.slice(0, 20); // 增加检测范围
    const headerHex = header.toString('hex');
    const headerStr = header.toString('ascii', 0, Math.min(12, header.length));

    console.log(`🔍 Audio format detection - Header (hex): ${headerHex}`);
    console.log(`🔍 Audio format detection - Header (ascii): ${headerStr}`);

    // WebM格式检测 - WebM文件通常以EBML头开始
    if (headerHex.startsWith('1a45dfa3') || // EBML signature
        header.includes(Buffer.from('webm')) ||
        headerStr.includes('webm')) {
      console.log(`✅ Detected WebM format`);
      return AudioFormat.WEBM;
    }

    // WAV格式检测
    if (header.includes(Buffer.from('RIFF')) && header.includes(Buffer.from('WAVE'))) {
      console.log(`✅ Detected WAV format`);
      return AudioFormat.WAV;
    }

    // OGG格式检测
    if (headerStr.startsWith('OggS')) {
      console.log(`✅ Detected OGG format`);
      return AudioFormat.OGG;
    }

    // MP3格式检测
    if (headerStr.startsWith('ID3') || headerHex.startsWith('fffa') || headerHex.startsWith('fffb')) {
      console.log(`✅ Detected MP3 format`);
      return AudioFormat.MP3;
    }

    // MP4格式检测
    if (headerHex.includes('66747970') || // 'ftyp' box
        headerStr.includes('ftyp') ||
        headerStr.includes('mp4')) {
      console.log(`✅ Detected MP4 format`);
      return AudioFormat.MP4;
    }

    // PCM格式检测 - 检查是否为原始音频数据
    // PCM数据通常没有特定的头部，但我们可以通过一些特征来判断
    const isLikelyPCM = this.isPCMData(buffer);
    if (isLikelyPCM) {
      console.log(`✅ Detected PCM format (raw audio data)`);
      return AudioFormat.PCM;
    }

    console.log(`❌ Could not detect audio format for buffer size: ${buffer.length} bytes`);
    return AudioFormat.UNKNOWN;
  }

  /**
   * 检测是否为PCM数据
   */
  private isPCMData(buffer: Buffer): boolean {
    // 检查缓冲区大小是否合理（通常是2的倍数，因为16位音频）
    if (buffer.length % 2 !== 0) {
      return false;
    }

    // 检查前几个字节是否全为零（可能是静音）
    const firstBytes = buffer.slice(0, Math.min(20, buffer.length));
    const allZeros = firstBytes.every(byte => byte === 0);

    // 如果前面都是零，可能是静音的PCM数据
    if (allZeros && buffer.length >= 1024) {
      console.log(`🔍 Detected likely PCM data (silence at start)`);
      return true;
    }

    // 检查数据是否看起来像16位PCM音频
    // PCM数据通常有一定的随机性，但不会有明显的文件头
    let hasVariation = false;
    let maxValue = 0;

    // 检查前1000字节的变化
    const sampleSize = Math.min(1000, buffer.length);
    for (let i = 0; i < sampleSize - 1; i += 2) {
      const sample = buffer.readInt16LE(i);
      const absSample = Math.abs(sample);
      maxValue = Math.max(maxValue, absSample);

      if (absSample > 100) { // 有一定的音频信号
        hasVariation = true;
      }
    }

    // 如果有音频变化且最大值在合理范围内，认为是PCM
    if (hasVariation && maxValue < 32768 && buffer.length >= 1024) {
      console.log(`🔍 Detected likely PCM data (audio variation detected, max: ${maxValue})`);
      return true;
    }

    // 如果缓冲区大小是常见的音频块大小，也可能是PCM
    const commonPCMSizes = [1024, 2048, 4096, 8192, 16384, 24576, 32768];
    if (commonPCMSizes.includes(buffer.length)) {
      console.log(`🔍 Detected likely PCM data (common PCM buffer size: ${buffer.length})`);
      return true;
    }

    return false;
  }

  /**
   * 处理并发送PCM音频给ASR
   */
  private async processAndSendPCMAudioForASR(sessionId: string): Promise<void> {
    const buffer = this.pcmAudioBuffers.get(sessionId);
    if (!buffer || buffer.chunks.length === 0 || buffer.isProcessing) {
      return;
    }

    buffer.isProcessing = true;
    const audioDataToProcess = Buffer.concat(buffer.chunks);

    // 重置缓冲区
    buffer.chunks = [];
    buffer.totalSize = 0;

    console.log(`[${sessionId}] Processing audio segment of size ${audioDataToProcess.length} for ASR`);

    try {
      const result = await this.asrServiceManager.recognize(audioDataToProcess);
      if (result && result.text.trim()) {
        console.log(`[${sessionId}] ASR result: ${result.text}`);

        // 处理ASR结果，添加到语音缓冲区
        await this.handleASRResult(sessionId, result);
      }
    } catch (error) {
      console.error(`[${sessionId}] Error processing audio for ASR:`, error);
      // 发送错误事件
      this.emit('asr_error', { sessionId, error });
    } finally {
      buffer.isProcessing = false;
    }
  }

  /**
   * 处理ASR结果
   */
  private async handleASRResult(sessionId: string, result: ASRResult): Promise<void> {
    console.log(`[${sessionId}] 处理ASR结果: ${result.text}`);

    if (!result.text || result.text.trim() === '') {
      console.log(`[${sessionId}] ASR结果为空，跳过处理`);
      return;
    }

    // 增强的文本清理
    let cleanedText = this.enhancedTextCleaning(result.text);
    console.log(`[${sessionId}] 清理后的文本: "${cleanedText}"`);

    if (!cleanedText || cleanedText.trim() === '') {
      console.log(`[${sessionId}] 清理后文本为空，跳过处理`);
      return;
    }

    // 使用增强的去重检测
    const isDuplicate = await this.isEnhancedDuplicate(sessionId, cleanedText);
    if (isDuplicate) {
      console.log(`[${sessionId}] 🚫 检测到重复文本，跳过: "${cleanedText}"`);
      return;
    }

    // 缓存文本用于后续去重检测
    this.cacheTextForDeduplication(sessionId, cleanedText);

    // 获取或创建语音缓冲区
    let speechBuffer = this.speechBuffers.get(sessionId);
    if (!speechBuffer) {
      speechBuffer = {
        transcriptions: [],
        lastTranscriptionTime: Date.now(),
        isProcessing: false
      };
      this.speechBuffers.set(sessionId, speechBuffer);
    }

    // 添加到语音缓冲区
    speechBuffer.transcriptions.push(cleanedText);
    speechBuffer.lastTranscriptionTime = Date.now();
    speechBuffer.lastTranscription = cleanedText;

    console.log(`[${sessionId}] 添加到语音缓冲区: "${cleanedText}"`);

    // 设置或重置超时处理
    if (speechBuffer.timeoutId) {
      clearTimeout(speechBuffer.timeoutId);
    }

    speechBuffer.timeoutId = setTimeout(async () => {
      console.log(`[${sessionId}] 语音暂停超时，处理完整语音`);
      await this.processCompleteSpeech(sessionId);
    }, this.SPEECH_PAUSE_TIMEOUT);
  }

  /**
   * 处理完整语音（3秒停顿后触发）
   */
  private async processCompleteSpeech(sessionId: string): Promise<void> {
    const buffer = this.speechBuffers.get(sessionId);
    if (!buffer || buffer.transcriptions.length === 0) {
      return;
    }

    // 防止重复处理
    if (buffer.isProcessing) {
      console.log(`⚠️ Speech processing already in progress for session ${sessionId}, skipping...`);
      return;
    }

    buffer.isProcessing = true;

    try {
      // 合并所有转录文本并去重
      const rawSpeech = buffer.transcriptions.join(' ').trim();
      const completeSpeech = this.removeDuplicateWords(rawSpeech);
      console.log(`📝 Complete speech for session ${sessionId}: "${completeSpeech}"`);

      // 检查是否与上次处理的文本相同，避免重复调用LLM
      if (buffer.lastProcessedText === completeSpeech) {
        console.log(`⚠️ Duplicate speech detected for session ${sessionId}, skipping LLM call: "${completeSpeech}"`);
        return;
      }

      // 清空缓冲区
      buffer.transcriptions = [];
      if (buffer.timeoutId) {
        clearTimeout(buffer.timeoutId);
        buffer.timeoutId = undefined;
      }

      // 记录已处理的文本
      buffer.lastProcessedText = completeSpeech;

      // 发送完整的转录结果给客户端
      this.emit('complete_transcription', {
        sessionId,
        text: completeSpeech,
        speaker: 'interviewer',
        timestamp: Date.now()
      });

      // 保存完整转录到数据库
      try {
        await prisma.interviewTranscript.create({
          data: {
            sessionId: sessionId,
            speaker: 'interviewer',
            content: completeSpeech,
          }
        });
        console.log(`💾 Complete transcription saved to database for session ${sessionId}`);
      } catch (dbError) {
        console.error('Error saving complete transcription to database:', dbError);
      }

      // 只有当文本有意义时才触发LLM（长度大于2且不是纯标点）
      if (completeSpeech.length > 2 && /[\u4e00-\u9fa5a-zA-Z0-9]/.test(completeSpeech)) {
        console.log(`🤖 Triggering LLM for session ${sessionId} with text: "${completeSpeech}"`);
        this.emit('llm_trigger', {
          sessionId,
          text: completeSpeech
        });
      } else {
        console.log(`⚠️ Skipping LLM call for session ${sessionId} - text too short or meaningless: "${completeSpeech}"`);
      }

    } finally {
      // 确保处理标志被重置
      buffer.isProcessing = false;
    }
  }

  /**
   * 增强的文本清理函数
   */
  private enhancedTextCleaning(text: string): string {
    if (!text || text.trim() === '') {
      return '';
    }

    console.log(`🧹 Original text before cleaning: "${text}"`);

    // 1. 基础清理：移除多余空格和特殊字符
    let cleaned = text.trim()
      .replace(/\s+/g, ' ')  // 多个空格合并为一个
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9，。！？、；：""''（）\s]/g, ''); // 保留中文、英文、数字和基本标点

    // 2. 应用增量去重算法
    cleaned = this.removeIncrementalDuplicates(cleaned);

    // 3. 移除明显的重复词汇模式
    cleaned = this.removeRepeatedWords(cleaned);

    // 4. 移除无意义的短语
    cleaned = this.removeNonsensePhrases(cleaned);

    console.log(`🧹 Text after enhanced cleaning: "${cleaned}"`);
    return cleaned;
  }

  /**
   * 清理会话资源
   */
  cleanupSession(sessionId: string): void {
    console.log(`[${sessionId}] Cleaning up audio processor resources`);

    // 处理剩余的语音缓冲区
    const speechBuffer = this.speechBuffers.get(sessionId);
    if (speechBuffer) {
      if (speechBuffer.timeoutId) {
        clearTimeout(speechBuffer.timeoutId);
      }
      // 如果有未处理的语音，先处理完
      if (speechBuffer.transcriptions.length > 0) {
        this.processCompleteSpeech(sessionId);
      }
      this.speechBuffers.delete(sessionId);
    }

    // 处理剩余的音频数据
    const pcmBuffer = this.pcmAudioBuffers.get(sessionId);
    if (pcmBuffer && pcmBuffer.chunks.length > 0 && !pcmBuffer.isProcessing) {
      console.log(`[${sessionId}] Processing remaining audio before cleanup`);
      this.processAndSendPCMAudioForASR(sessionId);
    }

    // 清理音频缓冲区
    this.audioBuffers.delete(sessionId);
    this.pcmAudioBuffers.delete(sessionId);

    // 清理新增的缓存
    this.slidingWindowCache.delete(sessionId);
    
    // 清理文本缓存中与该会话相关的条目
    const keysToDelete: string[] = [];
    this.textCache.forEach((value, key) => {
      if (key.startsWith(`${sessionId}:`)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => this.textCache.delete(key));

    console.log(`[${sessionId}] ✅ 增强去重缓存已清理，删除了 ${keysToDelete.length} 个文本缓存条目`);
    console.log(`[${sessionId}] Audio processor cleanup completed`);
  }

  /**
   * 移除重复词汇的函数
   */
  private removeRepeatedWords(text: string): string {
    if (!text) return '';

    // 按标点符号分割句子
    const sentences = text.split(/[，。！？；：]/).filter(s => s.trim());
    const cleanedSentences: string[] = [];

    for (const sentence of sentences) {
      const words = sentence.trim().split(/\s+/);
      const cleanedWords: string[] = [];

      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        // 检查是否与前面的词重复（允许最多1次重复）
        const lastOccurrence = cleanedWords.lastIndexOf(word);
        if (lastOccurrence === -1 || i - lastOccurrence > 2) {
          cleanedWords.push(word);
        } else {
          console.log(`🔧 Removed repeated word: "${word}"`);
        }
      }

      if (cleanedWords.length > 0) {
        cleanedSentences.push(cleanedWords.join(''));
      }
    }

    return cleanedSentences.join('，');
  }

  /**
   * 移除无意义短语的函数
   */
  private removeNonsensePhrases(text: string): string {
    if (!text) return '';

    // 定义无意义的短语模式
    const nonsensePatterns = [
      /然后呢再\w*然后呢再/g,  // "然后呢再...然后呢再"模式
      /\w*啊\w*啊/g,           // 重复的"啊"
      /画\w*画\w*画/g,         // 重复的"画"
      /化\w*化\w*化/g,         // 重复的"化"
      /划算\w*划算/g,          // 重复的"划算"
      /as\.?$/g,               // 结尾的"as"
      /^[，。！？；：\s]+/g,    // 开头的标点符号
      /[，。！？；：\s]+$/g     // 结尾多余的标点符号
    ];

    let cleaned = text;
    for (const pattern of nonsensePatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern, '');
      if (before !== cleaned) {
        console.log(`🔧 Removed nonsense pattern: "${before}" -> "${cleaned}"`);
      }
    }

    return cleaned.trim();
  }

  /**
   * 移除增量重复的函数
   */
  private removeIncrementalDuplicates(text: string): string {
    if (!text) return "";

    // 首先尝试专门的前缀式增量重复处理
    const result = this.removeXunfeiIncrementalDuplicates(text);
    if (result !== text) {
      return result;
    }

    // 如果没有处理，使用通用算法
    return this.removeGenericIncrementalDuplicates(text);
  }

  /**
   * 专门处理前缀式增量重复模式
   */
  private removeXunfeiIncrementalDuplicates(text: string): string {
    if (!text) return "";

    for (let k = 1; k <= text.length; k++) {
      const suffix = text.substring(text.length - k);
      const prefixPart = text.substring(0, text.length - k);

      if (prefixPart.length === 0) {
        continue;
      }

      // 创建suffix的所有前缀
      const prefixesOfSuffix: string[] = [];
      for (let i = 1; i <= suffix.length; i++) {
        prefixesOfSuffix.push(suffix.substring(0, i));
      }

      // 尝试用suffix的前缀重构prefixPart
      let j = 0;
      let canBeFormed = false;

      if (prefixPart.length === 0) {
        canBeFormed = true;
      } else {
        while (j < prefixPart.length) {
          let matchedThisIteration = false;
          for (let l = prefixesOfSuffix.length - 1; l >= 0; l--) {
            const currentSuffixPrefix = prefixesOfSuffix[l];
            if (prefixPart.startsWith(currentSuffixPrefix, j)) {
              j += currentSuffixPrefix.length;
              matchedThisIteration = true;
              break;
            }
          }
          if (!matchedThisIteration) {
            break;
          }
        }
        if (j === prefixPart.length) {
          canBeFormed = true;
        }
      }

      if (canBeFormed) {
        console.log(`🔧 Incremental duplicate removal: "${text}" -> "${suffix}"`);
        return suffix;
      }
    }

    return text;
  }

  /**
   * 通用增量重复处理算法
   */
  private removeGenericIncrementalDuplicates(text: string): string {
    let result = text;
    let changed = true;

    while (changed) {
      changed = false;
      const originalLength = result.length;

      for (let patternLen = 1; patternLen <= Math.min(8, Math.floor(result.length / 3)); patternLen++) {
        for (let i = 0; i <= result.length - patternLen * 2; i++) {
          const pattern = result.substring(i, i + patternLen);
          let nextPos = i + patternLen;
          let repeatCount = 0;

          while (nextPos <= result.length - patternLen &&
                 result.substring(nextPos, nextPos + patternLen) === pattern) {
            repeatCount++;
            nextPos += patternLen;
          }

          if (repeatCount > 0) {
            const beforePattern = result.substring(0, i);
            const afterRepeats = result.substring(i + patternLen * (repeatCount + 1));
            result = beforePattern + pattern + afterRepeats;
            changed = true;
            break;
          }
        }
        if (changed) break;
      }

      if (result.length === originalLength) {
        break;
      }
    }

    result = result.replace(/(.{1,3})\1{2,}/g, '$1');

    if (result !== text) {
      console.log(`🔧 Generic incremental duplicate removal: "${text}" -> "${result}"`);
    }

    return result;
  }

  /**
   * 文字去重函数
   */
  private removeDuplicateWords(text: string): string {
    if (!text || text.trim() === '') {
      return text;
    }

    console.log(`🧹 Original text: "${text}"`);

    // 处理增量重复问题
    let cleanedText = this.removeIncrementalDuplicates(text);

    // 分割成句子
    const sentences = cleanedText.split(/[。！？.!?]/).filter(s => s.trim() !== '');
    const cleanedSentences: string[] = [];

    for (const sentence of sentences) {
      const words = sentence.trim().split(/\s+/).filter(w => w !== '');
      if (words.length === 0) continue;

      // 去除连续重复的词语
      const cleanedWords: string[] = [];
      let lastWord = '';
      let repeatCount = 0;

      for (const word of words) {
        if (word === lastWord) {
          repeatCount++;
          if (repeatCount < 1) {
            cleanedWords.push(word);
          }
        } else {
          cleanedWords.push(word);
          lastWord = word;
          repeatCount = 0;
        }
      }

      if (cleanedWords.length > 0) {
        cleanedSentences.push(cleanedWords.join(''));
      }
    }

    let result = cleanedSentences.join('。');
    if (result && !result.match(/[。！？.!?]$/)) {
      if (cleanedText.match(/[。！？.!?]$/)) {
        result += '。';
      }
    }

    console.log(`🧹 Text deduplication result: "${result}"`);
    return result;
  }

  /**
   * 启动定期清理任务
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      const now = Date.now();

      // 检查PCM缓冲区是否需要处理
      this.pcmAudioBuffers.forEach((buffer, sessionId) => {
        if (!buffer.isProcessing &&
            buffer.chunks.length > 0 &&
            (now - buffer.lastChunkTime > this.SPEECH_PAUSE_TIMEOUT)) {
          console.log(`[${sessionId}] Speech pause detected, processing buffered audio`);
          this.processAndSendPCMAudioForASR(sessionId);
        }
      });
    }, this.SPEECH_PAUSE_TIMEOUT / 2);
  }

  /**
   * 增强的重复检测
   */
  private async isEnhancedDuplicate(sessionId: string, text: string): Promise<boolean> {
    try {
      // 生成文本指纹
      const fingerprint = this.textSimilarityDetector.generateTextFingerprint(text);
      
      // 检查缓存中是否有相似文本
      const cacheKey = `${sessionId}:${fingerprint.substring(0, 8)}`;
      const cached = this.textCache.get(cacheKey);
      
      if (cached) {
        // 计算相似度
        const similarity = this.textSimilarityDetector.calculateSimilarity(text, cached.text);
        
        if (similarity.editSimilarity > this.DUPLICATE_THRESHOLD || 
            similarity.cosineSimilarity > this.DUPLICATE_THRESHOLD) {
          console.log(`[${sessionId}] 🔍 相似度检测: 编辑距离=${similarity.editSimilarity.toFixed(3)}, 余弦=${similarity.cosineSimilarity.toFixed(3)}`);
          return true;
        }
      }
      
      // 检查滑动窗口中的文本
      const windowCache = this.slidingWindowCache.get(sessionId) || [];
      const currentTime = Date.now();
      
      // 清理过期的窗口数据
      const validWindow = windowCache.filter(item => 
        currentTime - item.timestamp < this.SLIDING_WINDOW_SIZE * 1000
      );
      
      // 检查窗口中是否有相似文本
      for (const item of validWindow) {
        const similarity = this.textSimilarityDetector.calculateSimilarity(text, item.text);
        const maxSimilarity = Math.max(
          similarity.editSimilarity,
          similarity.cosineSimilarity
        );
        
        if (maxSimilarity > this.DUPLICATE_THRESHOLD) {
          console.log(`[${sessionId}] 🔍 滑动窗口检测到重复: 最大相似度=${maxSimilarity.toFixed(3)}`);
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error(`[${sessionId}] 增强去重检测错误:`, error);
      return false;
    }
  }

  /**
   * 缓存文本用于去重检测
   */
  private cacheTextForDeduplication(sessionId: string, text: string): void {
    try {
      const fingerprint = this.textSimilarityDetector.generateTextFingerprint(text);
      const currentTime = Date.now();
      
      // 添加到主缓存
      const cacheKey = `${sessionId}:${fingerprint.substring(0, 8)}`;
      this.textCache.set(cacheKey, {
        text,
        timestamp: currentTime,
        fingerprint
      });
      
      // 添加到滑动窗口缓存
      let windowCache = this.slidingWindowCache.get(sessionId);
      if (!windowCache) {
        windowCache = [];
        this.slidingWindowCache.set(sessionId, windowCache);
      }
      
      // 计算相似度得分（用于窗口缓存）
      const similarity = this.textSimilarityDetector.calculateSimilarity(text, text);
      windowCache.push({
        text,
        timestamp: currentTime,
        similarity: Math.max(
          similarity.editSimilarity,
          similarity.cosineSimilarity
        )
      });
      
      // 清理过期的窗口数据
      const validWindow = windowCache.filter(item => 
        currentTime - item.timestamp < this.SLIDING_WINDOW_SIZE * 1000
      );
      this.slidingWindowCache.set(sessionId, validWindow);
      
      console.log(`[${sessionId}] 📝 文本已缓存用于去重检测: "${text.substring(0, 30)}..."`);
    } catch (error) {
      console.error(`[${sessionId}] 缓存文本错误:`, error);
    }
  }

  /**
   * 🔥 清理会话资源
   */
  async cleanupSession(sessionId: string): Promise<void> {
    console.log(`[${sessionId}] Cleaning up session resources...`);

    // 停止流式会话
    await this.stopStreamingSession(sessionId);

    // 清理缓冲区
    this.audioBuffers.delete(sessionId);
    this.pcmAudioBuffers.delete(sessionId);
    this.speechBuffers.delete(sessionId);
    this.slidingWindowCache.delete(sessionId);
    this.currentBubbles.delete(sessionId);

    console.log(`[${sessionId}] ✅ Session resources cleaned up`);
  }

  /**
   * 🔥 销毁处理器
   */
  async destroy(): Promise<void> {
    console.log('🔄 Destroying AudioProcessor...');

    // 停止所有流式会话
    const sessionIds = Array.from(this.streamingSessions.keys());
    for (const sessionId of sessionIds) {
      await this.stopStreamingSession(sessionId);
    }

    // 清理所有资源
    this.audioBuffers.clear();
    this.pcmAudioBuffers.clear();
    this.speechBuffers.clear();
    this.streamingSessions.clear();
    this.currentBubbles.clear();
    this.slidingWindowCache.clear();
    this.textCache.clear();

    // 🔥 修复：销毁所有会话专用的DashScope提供商
    for (const provider of this.sessionProviders.values()) {
      await provider.destroy();
    }
    this.sessionProviders.clear();

    // 销毁备用DashScope提供商
    await this.dashscopeProvider.destroy();

    console.log('✅ AudioProcessor destroyed');
  }
}
