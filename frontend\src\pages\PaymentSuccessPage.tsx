import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, ArrowLeft, RefreshCw } from 'lucide-react';
import useDocumentTitle from '../hooks/useDocumentTitle';
import { useToastContext } from '../contexts/ToastContext';
import { fetchWithAuth } from '../lib/api/apiService';

const PaymentSuccessPage: React.FC = () => {
  useDocumentTitle('支付结果');
  
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showSuccess, showError } = useToastContext();
  const [isLoading, setIsLoading] = useState(true);
  const [paymentResult, setPaymentResult] = useState<'success' | 'failed' | 'pending'>('pending');

  // 从URL参数获取订单信息
  const orderId = searchParams.get('orderId');

  // 检查支付结果
  useEffect(() => {
    const checkPaymentResult = async () => {
      if (!orderId) {
        setPaymentResult('failed');
        setIsLoading(false);
        return;
      }

      try {
        const data = await fetchWithAuth(`/payments/status/${orderId}`, {
          method: 'GET'
        });

        if (data.success) {
          if (data.status === 'COMPLETED') {
            setPaymentResult('success');
            showSuccess('支付成功！您的账户余额已更新');
          } else if (data.status === 'FAILED') {
            setPaymentResult('failed');
          } else {
            setPaymentResult('pending');
          }
        } else {
          setPaymentResult('failed');
        }
      } catch (error) {
        console.error('检查支付结果失败:', error);
        setPaymentResult('failed');
      } finally {
        setIsLoading(false);
      }
    };

    checkPaymentResult();
  }, [orderId, showSuccess]);

  // 返回充值中心
  const handleBackToPricing = () => {
    navigate('/pricing');
  };

  // 返回首页
  const handleBackToHome = () => {
    navigate('/');
  };

  // 重新检查支付状态
  const handleRecheck = () => {
    setIsLoading(true);
    // 重新触发检查
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <div className="bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl p-8 text-center">
          <RefreshCw className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
          <h1 className="text-xl font-bold text-gray-800 mb-2">正在确认支付结果</h1>
          <p className="text-gray-600">请稍候，正在验证您的支付状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-3">
      <div className="max-w-sm mx-auto">
        {/* 头部导航 */}
        <div className="flex items-center gap-3 mb-4 pt-2">
          <button
            onClick={handleBackToPricing}
            className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-lg font-bold text-gray-800">支付结果</h1>
        </div>

        {/* 支付结果卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl overflow-hidden"
        >
          <div className="p-6 text-center">
            {paymentResult === 'success' && (
              <div>
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-xl font-bold text-gray-800 mb-2">支付成功</h2>
                <p className="text-gray-600 mb-6">
                  恭喜您！支付已完成，相关服务已到账。
                </p>
                <div className="space-y-3">
                  <button
                    onClick={handleBackToPricing}
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all"
                  >
                    返回充值中心
                  </button>
                  <button
                    onClick={handleBackToHome}
                    className="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-50 transition-all"
                  >
                    返回首页
                  </button>
                </div>
              </div>
            )}

            {paymentResult === 'failed' && (
              <div>
                <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-red-500 text-2xl">✕</span>
                </div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">支付失败</h2>
                <p className="text-gray-600 mb-6">
                  很抱歉，支付未能完成。请重新尝试或联系客服。
                </p>
                <div className="space-y-3">
                  <button
                    onClick={handleBackToPricing}
                    className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all"
                  >
                    重新支付
                  </button>
                  <button
                    onClick={handleBackToHome}
                    className="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-50 transition-all"
                  >
                    返回首页
                  </button>
                </div>
              </div>
            )}

            {paymentResult === 'pending' && (
              <div>
                <div className="h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <RefreshCw className="h-8 w-8 text-orange-500" />
                </div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">支付处理中</h2>
                <p className="text-gray-600 mb-6">
                  您的支付正在处理中，请稍候或重新检查状态。
                </p>
                <div className="space-y-3">
                  <button
                    onClick={handleRecheck}
                    className="w-full bg-gradient-to-r from-orange-600 to-orange-700 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all"
                  >
                    重新检查
                  </button>
                  <button
                    onClick={handleBackToPricing}
                    className="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-50 transition-all"
                  >
                    返回充值中心
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* 帮助信息 */}
        <div className="mt-4 bg-white bg-opacity-70 backdrop-blur-md rounded-xl p-3">
          <h4 className="font-medium text-gray-800 mb-2 text-sm">温馨提示</h4>
          <ul className="text-xs text-gray-600 space-y-0.5">
            <li>• 支付成功后余额会立即到账</li>
            <li>• 如遇问题请联系客服</li>
            <li>• 支付记录可在个人中心查看</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
