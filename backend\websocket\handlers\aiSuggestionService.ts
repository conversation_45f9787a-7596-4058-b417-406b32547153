// AI建议服务
import axios from 'axios';
import { AuthenticatedWebSocket } from '../../types/websocket.js';
// import prisma from '../../lib/prisma.js'; // 暂时注释掉

export class AISuggestionService {
  private activeLLMRequests: Map<string, boolean> = new Map();
  private readonly DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

  constructor() {
    console.log('✅ AI Suggestion Service initialized');
  }

  /**
   * 🔥 生成AI建议 - 优化版本，支持岗位类型推断
   */
  async generateSuggestion(
    userText: string,
    sessionId: string,
    ws: AuthenticatedWebSocket,
    positionName?: string
  ): Promise<void> {
    // 检查是否已有相同的LLM请求在处理
    const isAlreadyProcessing = this.activeLLMRequests.get(sessionId);
    console.log(`🔍 LLM Request Status Check for session ${sessionId}:`, {
      isAlreadyProcessing: isAlreadyProcessing,
      userText: userText.substring(0, 50) + '...',
      textLength: userText.length,
      activeLLMRequestsSize: this.activeLLMRequests.size,
      allActiveSessions: Array.from(this.activeLLMRequests.keys())
    });

    if (isAlreadyProcessing) {
      console.log(`⚠️ LLM request already in progress for session ${sessionId}, skipping duplicate request`);
      return;
    }

    // 标记开始处理
    this.activeLLMRequests.set(sessionId, true);
    console.log(`🤖 Starting LLM generation for session ${sessionId}:`, {
      userText: `"${userText}"`,
      textLength: userText.length,
      timestamp: new Date().toISOString(),
      activeLLMRequestsAfterSet: this.activeLLMRequests.size
    });

    try {
      await this.callDeepSeekAPI(userText, sessionId, ws, positionName);
    } catch (error) {
      console.error(`❌ AI Suggestion error for session ${sessionId}:`, error);
      this.sendErrorMessage(ws, 'Failed to get AI suggestion', error);
    } finally {
      // 确保在任何情况下都清理LLM请求状态
      const wasActive = this.activeLLMRequests.delete(sessionId);
      console.log(`🧹 LLM request state cleaned for session ${sessionId}:`, {
        wasActiveBeforeDelete: wasActive,
        activeLLMRequestsAfterCleanup: this.activeLLMRequests.size,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 🔥 调用DeepSeek API - 修复版本：支持流式响应中断恢复
   */
  private async callDeepSeekAPI(
    userText: string,
    sessionId: string,
    ws: AuthenticatedWebSocket,
    positionName?: string
  ): Promise<void> {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      throw new Error('DEEPSEEK_API_KEY not configured');
    }

    // 🔥 新增：缓存部分响应，用于中断恢复
    let partialResponse = '';

    // 🔥 根据岗位推断用户类型
    const userType = this.inferUserType(positionName);
    console.log(`🎯 User type inferred for session ${sessionId}:`, {
      positionName,
      inferredUserType: userType
    });

    const messagesForLLM = [
      {
        role: 'system',
        content: this.getSystemPrompt(userType)
      },
      {
        role: 'user',
        content: `面试官说："${userText}"。请回答。`
      }
    ];

    try {
      console.log(`🤖 Calling DeepSeek API for session ${sessionId}:`, {
        userText: userText.substring(0, 50) + '...',
        textLength: userText.length,
        apiUrl: this.DEEPSEEK_API_URL,
        hasApiKey: !!apiKey,
        wsReadyState: ws.readyState
      });

      const response = await axios.post(
        this.DEEPSEEK_API_URL,
        {
          model: "deepseek-chat",
          messages: messagesForLLM,
          stream: true,
          // 🔥 速度优化参数
          temperature: 0.5, // 稍低的温度，在保证自然度的同时提升速度和一致性
          max_tokens: 150   // 严格限制最大Token，控制延迟的关键手段
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
          },
          responseType: 'stream',
          timeout: 15000 // 🔥 修复：缩短到15秒超时，提升响应性
        }
      );

      console.log(`✅ DeepSeek API response received for session ${sessionId}:`, {
        status: response.status,
        headers: response.headers['content-type'],
        hasData: !!response.data
      });

      await this.processStreamResponse(response, sessionId, ws, partialResponse);
    } catch (error) {
      // 🔥 修复：错误时发送部分响应（如果有的话）
      if (partialResponse.trim()) {
        console.log(`🔄 Sending partial response due to error for session ${sessionId}: "${partialResponse}"`);
        this.sendPartialResponse(ws, partialResponse);
      }
      this.handleAPIError(error, sessionId, ws);
    }
  }

  /**
   * 🔥 修复版本：处理流式响应，支持中断恢复
   */
  private async processStreamResponse(response: any, sessionId: string, ws: AuthenticatedWebSocket, partialResponse: string): Promise<void> {
    let accumulatedSuggestion = "";
    
    for await (const chunk of response.data) {
      const lines = chunk.toString('utf8').split('\n\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const jsonData = line.substring(6);
          
          if (jsonData.trim() === '[DONE]') {
            // 流式响应结束
            const endMessage = JSON.stringify({ type: 'ai_suggestion_end' });

            console.log(`📤 Sending AI suggestion end to client:`, {
              sessionId: sessionId,
              accumulatedLength: accumulatedSuggestion.length,
              wsReadyState: ws.readyState,
              messageSize: endMessage.length
            });

            if (ws.readyState === 1) { // WebSocket.OPEN
              ws.send(endMessage);
              console.log(`✅ AI suggestion end sent successfully`);
            } else {
              console.error(`❌ WebSocket not ready for sending end message, state: ${ws.readyState}`);
            }
            
            // 保存AI建议到数据库（暂时注释掉）
            if (accumulatedSuggestion.trim()) {
              await this.saveAISuggestion(accumulatedSuggestion.trim(), sessionId);
            }
            
            console.log(`✅ LLM suggestion completed for session ${sessionId}:`, {
              accumulatedLength: accumulatedSuggestion.length,
              timestamp: new Date().toISOString()
            });
            return;
          }
          
          try {
            const parsed = JSON.parse(jsonData);
            if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
              const contentChunk = parsed.choices[0].delta.content;
              accumulatedSuggestion += contentChunk;
              
              // 发送流式内容给客户端
              const chunkMessage = JSON.stringify({
                type: 'ai_suggestion_chunk',
                text: contentChunk
              });

              console.log(`📤 Sending AI suggestion chunk to client:`, {
                sessionId: sessionId,
                chunkLength: contentChunk.length,
                chunkPreview: contentChunk.substring(0, 20) + '...',
                wsReadyState: ws.readyState,
                messageSize: chunkMessage.length
              });

              if (ws.readyState === 1) { // WebSocket.OPEN
                ws.send(chunkMessage);
                console.log(`✅ AI suggestion chunk sent successfully`);
              } else {
                console.error(`❌ WebSocket not ready for sending, state: ${ws.readyState}`);
              }
            }
          } catch (parseError) {
            console.error('Error parsing DeepSeek stream chunk:', parseError, jsonData);
          }
        }
      }
    }
  }

  /**
   * 处理API错误
   */
  private handleAPIError(error: any, sessionId: string, ws: AuthenticatedWebSocket): void {
    console.error(`❌ DeepSeek LLM Error for session ${sessionId}:`, {
      errorMessage: error.message,
      errorResponse: error.response ? error.response.data : 'No response data',
      errorStatus: error.response ? error.response.status : 'No status',
      timestamp: new Date().toISOString()
    });

    let errorMessage = 'Failed to get AI suggestion';
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'AI suggestion request timeout';
      } else if (error.response?.status === 401) {
        errorMessage = 'AI service authentication failed';
      } else if (error.response?.status === 429) {
        errorMessage = 'AI service rate limit exceeded';
      } else if (error.response?.status >= 500) {
        errorMessage = 'AI service temporarily unavailable';
      }
    }

    this.sendErrorMessage(ws, errorMessage, error);
  }

  /**
   * 🔥 获取优化的系统提示词 - 基于岗位类型推断用户身份
   */
  private getSystemPrompt(userType: 'tech' | 'graduate' | 'general' = 'general'): string {
    // 技术岗位版
    if (userType === 'tech') {
      return `你是一名正在参加技术面试的软件工程师。你的回答必须严格遵循以下格式，关键词和回答内容之间必须空一行。

格式示例：
面试官：谈谈你对微服务的理解。
你的输出：
关键词：服务拆分、独立部署、API网关、数据一致性

嗯，我认为微服务核心是把一个大应用拆分成很多个小而独立的服务，每个服务都能自己开发和上线，它们之间通过API通信。这样能加快开发速度，也更容易维护。

---

现在，请根据面试官的问题，立即生成你的回答。`;
    }

    // 应届生优化版
    if (userType === 'graduate') {
      return `你是一名正在参加面试的应届生，聪明且积极。你的回答必须严格遵循以下格式，关键词和回答内容之间必须空一行。

格式示例：
面试官：你最大的缺点是什么？
你的输出：
关键词：经验不足、学习能力强、积极主动

作为应届生，我感觉我最大的不足是项目实战经验还不够丰富。不过我学习能力很强，也特别愿意主动去钻研，相信能很快上手。

---

现在，请根据面试官的问题，立即生成你的回答。`;
    }

    // 默认通用版
    return `你是一名求职者。你的回答必须严格遵循以下格式，关键词和回答内容之间必须空一行。

格式示例：
面试官：你的职业规划是什么？
你的输出：
关键词：职业发展、技能提升、长期目标

我希望能在这个岗位上不断学习和成长，提升自己的专业技能，为公司创造更大价值，同时也实现个人的职业发展目标。

---

现在，请根据面试官的问题，立即生成你的回答。`;
  }

  /**
   * 🔥 根据岗位名称推断用户类型
   */
  private inferUserType(positionName?: string): 'tech' | 'graduate' | 'general' {
    if (!positionName) return 'general';

    const position = positionName.toLowerCase();

    // 技术岗位关键词
    const techKeywords = [
      '开发', '工程师', 'engineer', 'developer', '程序员', 'programmer',
      '前端', 'frontend', '后端', 'backend', '全栈', 'fullstack',
      'java', 'python', 'javascript', 'react', 'vue', 'node',
      '算法', 'algorithm', '架构师', 'architect', 'devops',
      '测试', 'qa', 'tester', '运维', 'ops'
    ];

    // 应届生关键词
    const graduateKeywords = [
      '实习', 'intern', '应届', 'graduate', '校招', 'campus',
      '新人', 'junior', '初级', 'entry'
    ];

    // 检查应届生关键词
    if (graduateKeywords.some(keyword => position.includes(keyword))) {
      return 'graduate';
    }

    // 检查技术岗位关键词
    if (techKeywords.some(keyword => position.includes(keyword))) {
      return 'tech';
    }

    return 'general';
  }

  /**
   * 保存AI建议到数据库（暂时注释掉）
   */
  private async saveAISuggestion(text: string, sessionId: string): Promise<void> {
    try {
      // 暂时注释掉数据库操作
      // await prisma.aISuggestion.create({
      //   data: {
      //     text: text,
      //     sessionId: sessionId,
      //   }
      // });
      console.log(`💾 AI suggestion saved for session ${sessionId} (database operation commented out)`);
    } catch (dbError) {
      console.error('Error saving AI suggestion to database:', dbError);
    }
  }

  /**
   * 发送错误消息
   */
  private sendErrorMessage(ws: AuthenticatedWebSocket, message: string, error: any): void {
    const errorMessage = {
      type: 'ai_suggestion_error',
      message,
      error: error?.message || 'Unknown error',
      timestamp: Date.now()
    };
    
    if (ws.readyState === 1) { // WebSocket.OPEN
      ws.send(JSON.stringify(errorMessage));
    }
  }

  /**
   * 获取活跃请求状态
   */
  getActiveRequestsStatus(): { [sessionId: string]: boolean } {
    const status: { [sessionId: string]: boolean } = {};
    this.activeLLMRequests.forEach((isActive, sessionId) => {
      status[sessionId] = isActive;
    });
    return status;
  }

  /**
   * 清理会话
   */
  cleanupSession(sessionId: string): void {
    const wasActive = this.activeLLMRequests.delete(sessionId);
    if (wasActive) {
      console.log(`🧹 Cleaned up AI suggestion session: ${sessionId}`);
    }
  }

  /**
   * 🔥 新方法：发送部分响应 - 用于中断恢复
   */
  private sendPartialResponse(ws: AuthenticatedWebSocket, partialText: string): void {
    try {
      if (ws.readyState === 1) { // WebSocket.OPEN
        const message = JSON.stringify({
          type: 'ai_suggestion_partial',
          text: partialText,
          isPartial: true
        });
        ws.send(message);
        console.log(`📤 Sent partial response: "${partialText.substring(0, 50)}..."`);
      }
    } catch (error) {
      console.error('❌ Failed to send partial response:', error);
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    console.log('🧹 Destroying AI Suggestion Service');
    this.activeLLMRequests.clear();
    console.log('✅ AI Suggestion Service destroyed');
  }
}
