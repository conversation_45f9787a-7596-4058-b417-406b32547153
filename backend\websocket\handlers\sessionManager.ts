// 会话管理器 - 重构版本：延迟清理 + Redis锁 + 事务保护
import { AuthenticatedWebSocket, SessionInfo } from '../../types/websocket.js';
import prisma from '../../lib/prisma.js';
import { EventEmitter } from 'events';
import { ConnectionPool } from '../services/connectionPool.js';
import { RedisService } from '../../services/redisService.js';
import CleanupService from '../../services/CleanupService.js';
import { SessionConnectionManager } from '../managers/SessionConnectionManager.js';

// 🔥 新增：会话状态枚举
enum SessionStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  PENDING_CLEANUP = 'pending_cleanup',
  COMPLETED = 'completed',
  CLOSING = 'closing'
}

// 🔥 会话事件类型定义
interface SessionEvent {
  type: 'user.connected' | 'session.terminate' | 'session.create' | 'session.cleanup';
  data: {
    userId?: string;
    sessionId: string;
    gatewayId?: string;
    reason?: string;
    timestamp: number;
  };
}

// 🔥 会话编排服务 - 事件驱动架构核心
class SessionOrchestrator extends EventEmitter {
  private static instance: SessionOrchestrator;
  private activeOperations: Map<string, Promise<void>> = new Map();

  static getInstance(): SessionOrchestrator {
    if (!SessionOrchestrator.instance) {
      SessionOrchestrator.instance = new SessionOrchestrator();
    }
    return SessionOrchestrator.instance;
  }

  /**
   * 🔥 处理用户连接事件
   */
  async handleUserConnected(userId: string, sessionId: string, gatewayId: string): Promise<void> {
    const operationKey = `user-connect-${userId}`;

    // 🔥 防止并发操作冲突
    if (this.activeOperations.has(operationKey)) {
      console.log(`⏳ SessionOrchestrator: User ${userId} connection already in progress, waiting...`);
      await this.activeOperations.get(operationKey);
    }

    const operation = this.performUserConnection(userId, sessionId, gatewayId);
    this.activeOperations.set(operationKey, operation);

    try {
      await operation;
    } finally {
      this.activeOperations.delete(operationKey);
    }
  }

  /**
   * 🔥 执行用户连接逻辑
   */
  private async performUserConnection(userId: string, sessionId: string, gatewayId: string): Promise<void> {
    console.log(`🎭 SessionOrchestrator: Processing user connection`, {
      userId,
      sessionId,
      gatewayId,
      timestamp: Date.now()
    });

    // 🔥 第二阶段优化：先验证新连接的有效性
    const isNewSessionValid = await this.validateSessionInDatabase(sessionId, userId);

    if (!isNewSessionValid) {
      console.error(`❌ SessionOrchestrator: Invalid session ${sessionId} for user ${userId}, rejecting connection`);
      throw new Error(`Invalid session ${sessionId}. Please start the interview properly.`);
    }

    // 🔥 检查现有会话（通过事件查询，而非直接访问状态）
    const existingSession = await this.findExistingUserSession(userId);

    if (existingSession && existingSession.sessionId !== sessionId) {
      console.log(`🔄 SessionOrchestrator: Found existing session for user ${userId}, validating before termination...`);

      // 🔥 第二阶段优化：只有新会话有效时才终止旧会话
      console.log(`✅ SessionOrchestrator: New session ${sessionId} is valid, terminating old session ${existingSession.sessionId}`);

      // 🔥 发射会话终止事件
      this.emit('session.terminate', {
        type: 'session.terminate',
        data: {
          sessionId: existingSession.sessionId,
          reason: 'Valid new connection from same user',
          timestamp: Date.now()
        }
      });
    }

    // 🔥 发射会话创建事件
    this.emit('session.create', {
      type: 'session.create',
      data: {
        userId,
        sessionId,
        gatewayId,
        timestamp: Date.now()
      }
    });
  }

  /**
   * 🔥 查找用户现有会话（通过连接池查询）
   */
  private async findExistingUserSession(userId: string): Promise<{ sessionId: string } | null> {
    const connectionPool = ConnectionPool.getInstance();
    const userConnections = connectionPool.getUserConnections(userId);

    if (userConnections.length > 0) {
      // 获取第一个活跃连接的会话ID
      const connection = connectionPool.getConnection(userConnections[0]);
      return connection ? { sessionId: connection.sessionId } : null;
    }

    return null;
  }

  /**
   * 🔥 修复版本：验证会话在数据库中的有效性 - 允许新会话
   */
  private async validateSessionInDatabase(sessionId: string, userId: string): Promise<boolean> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      const session = await prisma.interviewSession.findUnique({
        where: { id: sessionId },
        select: { id: true, status: true, userId: true }
      });

      await prisma.$disconnect();

      if (!session) {
        console.log(`🆕 SessionOrchestrator: Session ${sessionId} not found in database, allowing new session creation`);
        // 🔥 修复：对于不存在的会话，允许创建（可能是新会话）
        return true;
      }

      if (session.userId !== userId) {
        console.log(`🔍 SessionOrchestrator: Session ${sessionId} belongs to different user`);
        return false;
      }

      if (session.status !== 'pending' && session.status !== 'active') {
        console.log(`🔍 SessionOrchestrator: Session ${sessionId} has invalid status: ${session.status}`);
        return false;
      }

      console.log(`✅ SessionOrchestrator: Session ${sessionId} is valid for user ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ SessionOrchestrator: Error validating session ${sessionId}:`, error);
      // 🔥 修复：发生错误时保守处理，允许连接
      return true;
    }
  }
}

export class SessionManager {
  private sessions: Map<string, SessionInfo> = new Map();
  private clientSessions: WeakMap<AuthenticatedWebSocket, string> = new WeakMap();
  private orchestrator: SessionOrchestrator;
  private connectionPool: ConnectionPool;
  private instanceId: string;
  private redis: RedisService;
  private cleanupService: CleanupService;
  private sessionConnectionManager: SessionConnectionManager;

  // 🔥 性能监控
  private stats = {
    totalSessions: 0,
    activeSessions: 0,
    sessionCreations: 0,
    sessionTerminations: 0,
    lastActivity: 0
  };

  constructor() {
    this.orchestrator = SessionOrchestrator.getInstance();
    this.connectionPool = ConnectionPool.getInstance();
    this.instanceId = `gateway-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    this.redis = RedisService.getInstance();
    this.cleanupService = new CleanupService();
    this.sessionConnectionManager = new SessionConnectionManager();
    this.setupEventHandlers();
    this.startPeriodicCleanup();

    console.log(`� SessionManager initialized with instance ID: ${this.instanceId}`);
  }

  /**
   * 🔥 启动定期清理任务
   */
  private startPeriodicCleanup(): void {
    // 每5分钟清理一次过期连接
    setInterval(() => {
      const cleanedCount = this.connectionPool.cleanupExpiredConnections();
      const sessionCleanedCount = this.cleanupExpiredSessions();

      if (cleanedCount > 0 || sessionCleanedCount > 0) {
        console.log(`🧹 Periodic cleanup completed: ${cleanedCount} connections, ${sessionCleanedCount} sessions`);
      }
    }, 5 * 60 * 1000); // 5分钟

    // 每30秒输出一次状态监控
    setInterval(() => {
      this.logSessionStatus();
      this.connectionPool.logStatus();
    }, 30 * 1000); // 30秒
  }

  /**
   * 🔥 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.orchestrator.on('session.terminate', async (event: SessionEvent) => {
      await this.handleSessionTerminate(event.data.sessionId, event.data.reason || 'Unknown');
    });

    this.orchestrator.on('session.create', (event: SessionEvent) => {
      // 这里可以处理会话创建的后续逻辑
      console.log(`📊 SessionManager: Session creation event received:`, event.data);
    });
  }

  /**
   * 🔥 简化的会话创建方法 - 事件驱动版本
   */
  async createSession(sessionId: string, userId: string, ws: AuthenticatedWebSocket, positionId?: string): Promise<void> {
    console.log(`� SessionManager: Creating session ${sessionId} for user ${userId} with position ${positionId || 'none'}`);

    // 🔥 通过事件编排器处理用户连接
    await this.orchestrator.handleUserConnected(userId, sessionId, this.instanceId);

    // 🔥 创建本地会话（简化版本）
    await this.createLocalSession(sessionId, userId, ws, positionId);

    // 🔥 更新统计
    this.updateStats('create');

    console.log(`✅ SessionManager: Session ${sessionId} connected successfully`);
  }

  /**
   * 🔥 创建本地会话（无复杂清理逻辑）
   */
  private async createLocalSession(sessionId: string, userId: string, ws: AuthenticatedWebSocket, positionId?: string): Promise<void> {
    // 🔥 通过连接池注册连接（自动处理冲突）
    const connectionId = `${sessionId}-${Date.now()}`;
    this.connectionPool.registerConnection(connectionId, userId, sessionId, this.instanceId);

    // 🔥 简单的会话创建，复杂逻辑由事件处理
    const newSession: SessionInfo = {
      sessionId,
      clients: new Set([ws]),
      createdAt: new Date(),
      lastActivity: new Date(),
      status: 'active'
    };

    this.sessions.set(sessionId, newSession);
    this.clientSessions.set(ws, sessionId);
    ws.sessionId = sessionId;
    ws.userId = userId;

    // 🔥 数据库操作（保持原有逻辑）
    await this.createDatabaseSession(sessionId, userId, positionId);
  }

  /**
   * 🔥 创建数据库会话记录
   */
  private async createDatabaseSession(sessionId: string, userId: string, positionId?: string): Promise<void> {
    try {
      let companyName: string | null = null;
      let positionName: string | null = null;
      let titleJobInfo = "Job Info from Client"; // 默认值，保持向后兼容

      // 如果提供了岗位ID，从数据库获取真实岗位信息
      if (positionId) {
        try {
          const position = await prisma.targetPosition.findFirst({
            where: {
              id: positionId,
              userId: userId, // 确保用户只能访问自己的岗位
            },
            select: {
              positionName: true,
              companyName: true,
            }
          });

          if (position) {
            companyName = position.companyName;
            positionName = position.positionName;
            titleJobInfo = `${position.positionName} - ${position.companyName}`;
            console.log(`📊 SessionManager: Found position data: ${titleJobInfo}`);
          } else {
            console.warn(`⚠️ SessionManager: Position not found for ID: ${positionId}`);
          }
        } catch (positionError) {
          console.error(`❌ SessionManager: Error fetching position ${positionId}:`, positionError);
        }
      }

      // 🔥 重构：WebSocket连接时只验证会话是否存在，不创建记录
      // 🔥 添加重试机制，等待数据库记录创建完成
      console.log(`🔍 DEBUG: Checking session ${sessionId} in database for user ${userId}`);

      let existingSession = null;
      let retryCount = 0;
      const maxRetries = 10; // 最多重试10次
      const retryDelay = 500; // 每次重试间隔500ms

      while (!existingSession && retryCount < maxRetries) {
        existingSession = await prisma.interviewSession.findUnique({
          where: { id: sessionId },
          select: { id: true, status: true, userId: true }
        });

        if (!existingSession) {
          retryCount++;
          console.log(`🔍 DEBUG: Session ${sessionId} not found, retry ${retryCount}/${maxRetries}`);

          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          }
        }
      }

      console.log(`🔍 DEBUG: Database query result after ${retryCount} retries:`, existingSession);

      if (!existingSession) {
        console.error(`❌ DEBUG: Session ${sessionId} not found in database after ${maxRetries} retries`);
        throw new Error(`Session ${sessionId} not found. Please start the interview first.`);
      }

      if (existingSession.userId !== userId) {
        console.error(`❌ DEBUG: Session ${sessionId} belongs to user ${existingSession.userId}, not ${userId}`);
        throw new Error(`Session ${sessionId} does not belong to user ${userId}`);
      }

      console.log(`✅ DEBUG: Session ${sessionId} found with status: ${existingSession.status}`);

      if (existingSession.status !== 'pending') {
        console.log(`⚠️ DEBUG: Session ${sessionId} status is ${existingSession.status}, updating to active`);
      }

      // 🔥 WebSocket连接成功后，更新状态为active（如果还不是的话）
      if (existingSession.status === 'pending') {
        console.log(`🔄 DEBUG: Updating session ${sessionId} status from pending to active`);
        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            status: 'active',
            startedAt: new Date(),
          }
        });
        console.log(`✅ DEBUG: Session ${sessionId} status updated from pending to active`);
      } else {
        console.log(`ℹ️ DEBUG: Session ${sessionId} already has status ${existingSession.status}, no update needed`);
      }
      console.log(`📊 SessionManager: Session ${sessionId} validated and activated`);
    } catch (dbError) {
      console.error(`❌ SessionManager: Database error for session ${sessionId}:`, dbError);
      // 不要抛出错误，继续处理WebSocket连接
      console.log(`⚠️ SessionManager: Continuing without database session for ${sessionId}`);
    }
  }

  /**
   * 🔥 处理会话终止事件
   */
  private async handleSessionTerminate(sessionId: string, reason: string): Promise<void> {
    console.log(`🔄 SessionManager: Handling session termination: ${sessionId}, reason: ${reason}`);

    const session = this.sessions.get(sessionId);
    if (session) {
      // 🔥 首先清理ASR连接，避免僵尸任务
      try {
        await this.sessionConnectionManager.closeSessionConnection(sessionId, reason);
        console.log(`✅ SessionManager: ASR connection cleaned for ${sessionId}`);
      } catch (error) {
        console.error(`❌ SessionManager: Failed to clean ASR connection for ${sessionId}:`, error);
      }

      // 关闭所有客户端连接
      session.clients.forEach(client => {
        try {
          client.close(4001, `Session terminated: ${reason}`);
        } catch (error) {
          console.error(`❌ Failed to close client:`, error);
        }
      });

      // 删除会话
      this.sessions.delete(sessionId);
      this.updateStats('terminate');

      console.log(`✅ SessionManager: Session ${sessionId} terminated successfully`);
    }
  }

  /**
   * 🔥 更新性能统计
   */
  private updateStats(operation: 'create' | 'terminate'): void {
    this.stats.lastActivity = Date.now();
    this.stats.activeSessions = this.sessions.size;

    if (operation === 'create') {
      this.stats.sessionCreations++;
      this.stats.totalSessions++;
    } else if (operation === 'terminate') {
      this.stats.sessionTerminations++;
    }
  }

  /**
   * 🔥 获取性能统计
   */
  getPerformanceStats(): typeof this.stats & { instanceId: string; timestamp: number } {
    return {
      ...this.stats,
      instanceId: this.instanceId,
      timestamp: Date.now()
    };
  }

  /**
   * 🔥 输出会话监控日志
   */
  logSessionStatus(): void {
    const stats = this.getPerformanceStats();
    console.log('📊 SessionManager Status:', {
      ...stats,
      sessionsDetail: Array.from(this.sessions.entries()).map(([id, session]) => ({
        sessionId: id,
        clientCount: session.clients.size,
        status: session.status,
        uptime: Date.now() - session.createdAt.getTime()
      }))
    });
  }

  /**
   * 🔥 清理过期会话
   */
  cleanupExpiredSessions(maxIdleTime: number = 30 * 60 * 1000): number { // 30分钟默认
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      const idleTime = now - session.lastActivity.getTime();

      if (idleTime > maxIdleTime) {
        console.log(`🧹 SessionManager: Cleaning up expired session ${sessionId}, idle for ${Math.round(idleTime / 1000)}s`);

        // 关闭所有客户端
        session.clients.forEach(client => {
          try {
            client.close(4000, 'Session expired due to inactivity');
          } catch (error) {
            console.error(`❌ Failed to close expired client:`, error);
          }
        });

        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.updateStats('terminate');
      console.log(`🧹 SessionManager: Cleaned up ${cleanedCount} expired sessions`);
    }

    return cleanedCount;
  }

  /**
   * 获取会话信息
   */
  getSession(sessionId: string): SessionInfo | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 获取客户端所属会话ID
   */
  getClientSession(ws: AuthenticatedWebSocket): string | undefined {
    return this.clientSessions.get(ws);
  }

  /**
   * 获取会话的所有客户端
   */
  getSessionClients(sessionId: string): Set<AuthenticatedWebSocket> | undefined {
    const session = this.sessions.get(sessionId);
    return session?.clients;
  }

  /**
   * 向会话广播消息
   */
  broadcastToSession(sessionId: string, message: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`Session ${sessionId} not found for broadcast`);
      return;
    }

    session.clients.forEach(client => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(message);
      }
    });

    session.lastActivity = new Date();
  }

  /**
   * 移除客户端 - 🔥 修复版本：延迟清理 + Redis锁 + 事务保护
   */
  async removeClient(ws: AuthenticatedWebSocket): Promise<void> {
    const sessionId = this.clientSessions.get(ws);
    if (!sessionId) {
      return;
    }

    console.log(`🔌 SessionManager: Removing client from session ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      this.clientSessions.delete(ws);
      return;
    }

    // 移除客户端连接
    session.clients.delete(ws);
    this.clientSessions.delete(ws);

    // 🔥 核心修复：如果没有客户端了，启动延迟清理机制
    if (session.clients.size === 0) {
      await this.handleSessionEndWithDelayedCleanup(sessionId);
    }
  }

  /**
   * 🔥 新方法：延迟清理机制 - 解决连接稳定性问题的核心
   */
  private async handleSessionEndWithDelayedCleanup(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== SessionStatus.ACTIVE) {
      return;
    }

    console.log(`🔄 SessionManager: Starting delayed cleanup for session ${sessionId}`);

    // 🔥 步骤1：标记为pending_cleanup状态，避免立即删除
    session.status = SessionStatus.PENDING_CLEANUP;

    // 🔥 步骤2：使用Redis锁防止并发清理
    const lockKey = `session_lock:${sessionId}`;
    const redisClient = this.redis.getClient();

    try {
      const lockAcquired = await redisClient.set(lockKey, 'locked', 'NX', 'EX', 30); // 30秒锁
      if (!lockAcquired) {
        console.warn(`⚠️ SessionManager: Session ${sessionId} already locked, skipping cleanup`);
        return;
      }

      // 🔥 步骤3：清理ASR连接，避免僵尸任务
      try {
        await this.sessionConnectionManager.closeSessionConnection(sessionId, 'Session ended');
        console.log(`✅ SessionManager: ASR connection cleaned for ${sessionId}`);
      } catch (error) {
        console.error(`❌ SessionManager: Failed to clean ASR connection for ${sessionId}:`, error);
      }

      // 🔥 步骤4：使用Prisma事务原子更新数据库
      await this.updateSessionInDatabaseWithTransaction(sessionId, SessionStatus.COMPLETED);

      // 🔥 步骤5：延迟删除内存状态（给新连接缓冲时间）
      const cleanupDelay = parseInt(process.env.CLEANUP_DELAY || '10000'); // 默认10秒，可配置
      setTimeout(async () => {
        try {
          this.sessions.delete(sessionId);
          await redisClient.del(lockKey);
          console.log(`✅ SessionManager: Session ${sessionId} cleanup completed after ${cleanupDelay}ms delay`);
        } catch (error) {
          console.error(`❌ SessionManager: Error in delayed cleanup for ${sessionId}:`, error);
        }
      }, cleanupDelay);

      console.log(`✅ SessionManager: Session ${sessionId} marked for delayed cleanup (${cleanupDelay}ms)`);

    } catch (error) {
      console.error(`❌ SessionManager: Failed delayed cleanup for ${sessionId}:`, error);
      // 回滚状态，允许重试
      if (session) {
        session.status = SessionStatus.ACTIVE;
      }
      await redisClient.del(lockKey);
      // 可选：触发重试机制或fallback到同步清理
      await this.fallbackSyncCleanup(sessionId);
    }
  }

  /**
   * 🔥 新方法：使用Prisma事务更新数据库，确保原子性
   */
  private async updateSessionInDatabaseWithTransaction(sessionId: string, status: SessionStatus): Promise<void> {
    try {
      await prisma.$transaction(async (tx) => {
        await tx.interviewSession.update({
          where: { id: sessionId },
          data: {
            status: status,
            endedAt: new Date()
          }
        });

        console.log(`✅ SessionManager: Database transaction completed for session ${sessionId}, status: ${status}`);
      });
    } catch (error) {
      console.error(`❌ SessionManager: Database transaction failed for session ${sessionId}:`, error);
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 回退同步清理（当异步清理失败时）
   */
  private async fallbackSyncCleanup(sessionId: string): Promise<void> {
    console.log(`⚠️ SessionManager: Performing fallback sync cleanup for ${sessionId}`);

    try {
      await prisma.interviewSession.update({
        where: { id: sessionId },
        data: {
          status: 'completed',
          endedAt: new Date()
        }
      });

      // 从内存中移除
      this.sessions.delete(sessionId);

      console.log(`✅ SessionManager: Fallback cleanup completed for ${sessionId}`);
    } catch (dbError) {
      console.error(`❌ SessionManager: Fallback cleanup failed for ${sessionId}:`, dbError);
    }
  }

  /**
   * 获取活跃会话数量
   */
  getActiveSessionCount(): number {
    return this.sessions.size;
  }




}
