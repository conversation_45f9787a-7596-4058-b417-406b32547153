import React, { useState, useRef, useCallback, useEffect } from 'react';
import useAuthStore from '../../stores/authStore';

interface VoiceInputButtonProps {
  onTextReceived: (text: string) => void;
  disabled?: boolean;
  className?: string;
  webSocket?: WebSocket | null; // 接收现有的WebSocket连接
  onSendMessage?: (message: any) => void; // 发送消息的回调
}

interface RecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
}

/**
 * 语音输入按钮组件
 * 使用Web Speech API进行语音识别
 */
const VoiceInputButton: React.FC<VoiceInputButtonProps> = ({
  onTextReceived,
  disabled = false,
  className = '',
  webSocket = null,
  onSendMessage = null
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // 🔥 使用Web Speech API进行语音识别
  const recognitionRef = useRef<any>(null);
  const [isListening, setIsListening] = useState(false);

  /**
   * 检查Web Speech API支持
   */
  const checkSpeechRecognitionSupport = useCallback(() => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      setError('您的浏览器不支持语音识别功能，请使用Chrome浏览器');
      setHasPermission(false);
      return false;
    }
    setHasPermission(true);
    setError(null);
    return true;
  }, []);

  /**
   * 初始化语音识别
   */
  const initSpeechRecognition = useCallback(() => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) return null;

    const recognition = new SpeechRecognition();
    
    // 配置语音识别
    recognition.continuous = true;          // 持续识别
    recognition.interimResults = true;      // 返回中间结果
    recognition.lang = 'zh-CN';            // 中文识别
    recognition.maxAlternatives = 1;        // 最多返回1个结果

    // 识别开始
    recognition.onstart = () => {
      console.log('🎤 语音识别已开始');
      setIsListening(true);
      setError(null);
    };

    // 识别结果
    recognition.onresult = (event: any) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        console.log('🎵 语音识别结果:', finalTranscript);
        onTextReceived(finalTranscript);
      }
    };

    // 识别结束
    recognition.onend = () => {
      console.log('🔇 语音识别已结束');
      setIsListening(false);
      setIsProcessing(false);
    };

    // 识别错误
    recognition.onerror = (event: any) => {
      console.error('🎤 语音识别错误:', event.error);
      setIsListening(false);
      setIsProcessing(false);
      
      switch (event.error) {
        case 'not-allowed':
          setError('请允许访问麦克风以使用语音输入功能');
          break;
        case 'no-speech':
          setError('未检测到语音，请重试');
          break;
        case 'network':
          setError('网络错误，请检查网络连接');
          break;
        default:
          setError(`语音识别失败: ${event.error}`);
      }
    };

    return recognition;
  }, [onTextReceived]);

  /**
   * 开始语音识别
   */
  const startSpeechRecognition = useCallback(() => {
    try {
      // 检查支持
      if (!checkSpeechRecognitionSupport()) {
        return;
      }

      // 初始化识别器
      const recognition = initSpeechRecognition();
      if (!recognition) {
        setError('语音识别初始化失败');
        return;
      }

      recognitionRef.current = recognition;
      
      // 开始识别
      recognition.start();
      setIsRecording(true);
      console.log('🎤 开始语音识别');

    } catch (err: any) {
      console.error('开始语音识别失败:', err);
      setError(err.message || '语音识别启动失败');
    }
  }, [checkSpeechRecognitionSupport, initSpeechRecognition]);

  /**
   * 停止语音识别
   */
  const stopSpeechRecognition = useCallback(() => {
    try {
      if (recognitionRef.current && isListening) {
        recognitionRef.current.stop();
        setIsRecording(false);
        console.log('🔇 停止语音识别');
      }
    } catch (err) {
      console.error('停止语音识别失败:', err);
      setError('停止语音识别失败');
    }
  }, [isListening]);

  /**
   * 清理资源
   */
  const cleanup = useCallback(() => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      } catch (err) {
        console.warn('清理语音识别资源失败:', err);
      }
    }
    setIsRecording(false);
    setIsListening(false);
    setIsProcessing(false);
  }, []);

  // 组件卸载时清理资源
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  /**
   * 切换录音状态
   */
  const toggleRecording = useCallback(() => {
    if (disabled) return;

    if (isRecording || isListening) {
      stopSpeechRecognition();
    } else {
      startSpeechRecognition();
    }
  }, [disabled, isRecording, isListening, startSpeechRecognition, stopSpeechRecognition]);

  // 获取按钮样式
  const getButtonStyle = () => {
    if (disabled || hasPermission === false) {
      return 'bg-transparent text-gray-400 cursor-not-allowed border border-gray-300';
    }
    if (isRecording || isListening) {
      return 'bg-red-500 text-white animate-pulse hover:bg-red-600 border border-red-500';
    }
    if (isProcessing) {
      return 'bg-yellow-500 text-white border border-yellow-500';
    }
    return 'bg-transparent text-gray-600 hover:text-blue-600 hover:bg-blue-50 border border-gray-300';
  };

  // 获取按钮图标
  const getButtonIcon = () => {
    if (isProcessing) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2v4m0 12v4m10-10h-4M6 12H2m15.364-7.364l-2.828 2.828M9.464 9.464L6.636 6.636m12.728 12.728l-2.828-2.828M9.464 14.536l-2.828 2.828" stroke="currentColor" strokeWidth="2" fill="none" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
        <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" strokeWidth="2" fill="none" />
        <line x1="12" y1="19" x2="12" y2="23" stroke="currentColor" strokeWidth="2" />
        <line x1="8" y1="23" x2="16" y2="23" stroke="currentColor" strokeWidth="2" />
      </svg>
    );
  };

  // 获取提示文本
  const getTooltipText = () => {
    if (disabled) return '语音输入不可用';
    if (hasPermission === false) return error || '浏览器不支持语音识别';
    if (isRecording || isListening) return '点击停止语音识别';
    if (isProcessing) return '正在处理语音...';
    return '点击开始语音识别';
  };

  return (
    <div className="relative">
      <button
        onClick={toggleRecording}
        disabled={disabled || hasPermission === false}
        className={`p-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${getButtonStyle()} ${className}`}
        title={getTooltipText()}
        aria-label={getTooltipText()}
      >
        {getButtonIcon()}
      </button>

      {/* 错误提示 */}
      {error && (
        <div className="absolute top-full left-0 mt-1 p-2 bg-red-100 text-red-700 text-xs rounded shadow-lg whitespace-nowrap z-10">
          {error}
        </div>
      )}
    </div>
  );
};

export default VoiceInputButton;