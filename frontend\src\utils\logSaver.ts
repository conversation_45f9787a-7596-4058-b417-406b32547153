// 前端日志保存工具
export class LogSaver {
  private logs: string[] = [];
  private isCapturing = false;
  private originalConsole: {
    log: typeof console.log;
    error: typeof console.error;
    warn: typeof console.warn;
    info: typeof console.info;
  };

  constructor() {
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };
  }

  // 开始捕获控制台日志
  startCapturing() {
    if (this.isCapturing) return;
    
    this.isCapturing = true;
    this.logs = [];

    // 重写console方法
    console.log = (...args) => {
      this.addLog('LOG', args);
      this.originalConsole.log(...args);
    };

    console.error = (...args) => {
      this.addLog('ERROR', args);
      this.originalConsole.error(...args);
    };

    console.warn = (...args) => {
      this.addLog('WARN', args);
      this.originalConsole.warn(...args);
    };

    console.info = (...args) => {
      this.addLog('INFO', args);
      this.originalConsole.info(...args);
    };
  }

  // 停止捕获控制台日志
  stopCapturing() {
    if (!this.isCapturing) return;

    this.isCapturing = false;

    // 恢复原始console方法
    console.log = this.originalConsole.log;
    console.error = this.originalConsole.error;
    console.warn = this.originalConsole.warn;
    console.info = this.originalConsole.info;
  }

  // 添加日志条目
  private addLog(level: string, args: any[]) {
    const timestamp = new Date().toISOString();
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    
    this.logs.push(`[${timestamp}] ${level}: ${message}`);
  }

  // 获取所有日志
  getLogs(): string[] {
    return [...this.logs];
  }

  // 清空日志
  clearLogs() {
    this.logs = [];
  }

  // 生成日志文件名
  private generateFileName(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    
    return `frontend-${year}-${month}-${day}-${hour}-${minute}-${second}.txt`;
  }

  // 保存日志到文件（通过API）
  async saveToFile(): Promise<void> {
    if (this.logs.length === 0) {
      console.warn('没有日志可保存');
      return;
    }

    // 🔥 修复：检查auth token
    const { useAuthStore } = require('../stores/authStore');
    const token = useAuthStore.getState().token;

    if (!token) {
      console.warn('⚠️ ClientLogger: No auth token, skipping log upload');
      return;
    }

    const fileName = this.generateFileName();

    try {
      // 通过API保存到服务器指定目录
      const API_BASE_URL = process.env.NODE_ENV === 'production'
        ? 'https://mianshijun.xyz'
        : '';

      const response = await fetch(`${API_BASE_URL}/api/logs/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          logs: this.logs,
          type: 'frontend'
        }),
      });

      if (!response.ok) {
        throw new Error(`保存失败: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ 前端日志已自动保存: ${result.fileName} (${result.entriesCount} 条记录)`);
    } catch (error) {
      console.error('保存日志失败:', error);
      // 移除自动下载回退，避免弹窗干扰用户
      console.warn('日志保存到服务器失败，已跳过本地下载以避免干扰用户体验');
    }
  }

  // 回退到下载方式
  private fallbackToDownload(fileName: string): void {
    const logContent = [
      `Frontend Console Log`,
      `Generated: ${new Date().toISOString()}`,
      `Total entries: ${this.logs.length}`,
      '',
      '=== CONSOLE OUTPUT ===',
      '',
      ...this.logs
    ].join('\n');

    try {
      const blob = new Blob([logContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      URL.revokeObjectURL(url);

      console.log(`日志已下载为: ${fileName}`);
    } catch (error) {
      console.error('下载日志失败:', error);
    }
  }

  // 定时保存日志（每5分钟）
  setupAutoSave() {
    // 每5分钟自动保存一次（仅在开发环境且日志较多时）
    setInterval(() => {
      if (this.logs.length > 100) { // 提高阈值，减少自动保存频率
        console.log(`自动保存日志: ${this.logs.length} 条记录`);
        this.saveToFile();
        this.clearLogs(); // 保存后清空日志
      }
    }, 10 * 60 * 1000); // 改为10分钟，减少频率

    // 页面卸载时尝试同步保存（但不依赖异步API）
    window.addEventListener('beforeunload', () => {
      if (this.logs.length > 0) {
        // 使用同步方式发送日志（navigator.sendBeacon）
        this.sendLogsSync();
      }
    });
  }

  // 同步发送日志（用于页面卸载时）
  private sendLogsSync() {
    try {
      const API_BASE_URL = process.env.NODE_ENV === 'production'
        ? 'https://mianshijun.xyz'
        : '';

      const data = JSON.stringify({
        logs: this.logs,
        fileName: this.generateFileName()
      });

      // 使用 sendBeacon 进行同步发送
      const blob = new Blob([data], { type: 'application/json' });
      navigator.sendBeacon(`${API_BASE_URL}/api/frontend-logs`, blob);
    } catch (error) {
      console.error('同步发送日志失败:', error);
    }
  }
}

// 创建全局实例
export const logSaver = new LogSaver();

// 防止重复初始化
if (!(window as any).__logSaverInitialized) {
  (window as any).__logSaverInitialized = true;

  // 开发环境下自动启用日志捕获（但不自动保存）
  if (process.env.NODE_ENV === 'development') {
    logSaver.startCapturing();
    // 完全禁用自动保存，避免弹窗干扰
    // logSaver.setupAutoSave();

    // 添加快捷键 Ctrl+Shift+L 手动保存日志
    window.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        console.log('手动保存日志...');
        logSaver.saveToFile();
      }
    });

    // 添加全局方法供开发者控制台使用
    (window as any).saveLogs = () => {
      console.log('手动保存日志...');
      logSaver.saveToFile();
    };
    (window as any).clearLogs = () => logSaver.clearLogs();
    console.log('开发工具已启用：');
    console.log('- 按 Ctrl+Shift+L 手动保存日志');
    console.log('- 在控制台输入 saveLogs() 手动保存');
    console.log('- 在控制台输入 clearLogs() 清空日志');
    console.log('- 注意：已完全禁用自动保存，避免弹窗干扰');
  }
}
