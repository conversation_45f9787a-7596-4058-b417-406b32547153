import { Router, Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import { UsageType } from '@prisma/client';
import { RedisService } from '../services/redisService';
import '../utils/logger'; // 导入logger配置

const router = Router();
// 🔥 新增：Redis服务实例用于状态检查
const redisService = new RedisService();

/**
 * 开始模拟面试 - 原子性扣费和会话创建
 * POST /api/interviews/start-mock
 */
router.post('/start-mock', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  console.log('🎯 Interviews route: /start-mock handler called');
  console.log('🎯 Request body:', req.body);
  console.log('🎯 User:', req.user);

  try {
    const userId = req.user!.userId;
    const { sessionId, config } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: '会话ID不能为空'
      });
    }

    if (!config || !config.companyName || !config.positionName) {
      return res.status(400).json({
        success: false,
        message: '面试配置信息不完整'
      });
    }

    console.log(`🚀 DEBUG: Starting mock interview for user ${userId}, session ${sessionId}`);

    // 🔥 使用事务确保原子性操作
    const result = await prisma.$transaction(async (tx) => {
      // 1. 检查用户余额
      const userBalance = await tx.userBalance.findUnique({
        where: { userId: userId }
      });

      if (!userBalance) {
        throw new Error('用户余额信息不存在');
      }

      if (userBalance.mockInterviewCredits < 1) {
        throw new Error('模拟面试次数不足，请先充值');
      }

      // 2. 扣除模拟面试次数
      const updatedBalance = await tx.userBalance.update({
        where: { userId: userId },
        data: {
          mockInterviewCredits: {
            decrement: 1
          }
        }
      });

      console.log(`💰 DEBUG: Mock interview credits deducted. New balance: ${updatedBalance.mockInterviewCredits}`);

      // 3. 创建面试会话记录（初始状态为pending）
      console.log(`📝 DEBUG: Creating mock interview session with id: ${sessionId}`);
      const interviewSession = await tx.interviewSession.create({
        data: {
          id: sessionId,
          userId: userId,
          status: 'pending', // 🔥 初始状态为pending
          titleJobInfo: `${config.positionName} - ${config.companyName}`,
          companyName: config.companyName,
          positionName: config.positionName,
          startedAt: null, // pending状态下startedAt为null
        }
      });

      // 4. 创建消费记录
      await tx.usageRecord.create({
        data: {
          userId: userId,
          type: UsageType.MOCK_INTERVIEW,
          amount: -1, // 负数表示消耗
          reason: `模拟面试 - ${config.positionName} - ${config.companyName}`,
        }
      });

      return {
        session: interviewSession,
        newBalance: updatedBalance
      };
    }, {
      timeout: 10000, // 10秒超时
      isolationLevel: 'ReadCommitted'
    });

    // 5. 在Redis中标记会话状态
    const redis = RedisService.getInstance();
    const redisClient = redis.getClient();
    await redisClient.set(`session:${sessionId}:status`, 'pending', 'EX', 3600); // 1小时过期

    console.log(`✅ Mock interview session created: ${sessionId} for user ${userId}`);

    return res.status(200).json({
      success: true,
      message: '模拟面试会话创建成功',
      data: {
        sessionId: result.session.id,
        status: result.session.status,
        position: {
          companyName: result.session.companyName,
          positionName: result.session.positionName,
          titleJobInfo: result.session.titleJobInfo
        },
        newBalance: {
          formalInterviewCredits: result.newBalance.formalInterviewCredits,
          mockInterviewCredits: result.newBalance.mockInterviewCredits,
          mianshijunBalance: result.newBalance.mianshijunBalance
        }
      }
    });

  } catch (error: any) {
    console.error(`❌ Failed to start mock interview:`, error);

    if (error.message.includes('模拟面试次数不足')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: '启动模拟面试失败，请稍后重试'
    });
  }
});

/**
 * 开始正式面试 - 原子性扣费和会话创建
 * POST /api/interviews/start
 */
router.post('/start', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  console.log('🎯 Interviews route: /start handler called');
  console.log('🎯 Request body:', req.body);
  console.log('🎯 User:', req.user);

  try {
    const userId = req.user!.userId;
    const { positionId, sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: '会话ID不能为空'
      });
    }

    console.log(`🚀 DEBUG: Starting formal interview for user ${userId}, session ${sessionId}, positionId: ${positionId}`);

    // 🔥 使用事务确保扣费和会话创建的原子性
    const result = await prisma.$transaction(async (tx) => {
      console.log(`🔍 DEBUG: Starting transaction for user ${userId}`);

      // 1. 验证并扣除正式面试次数
      console.log(`💰 DEBUG: Checking user balance for ${userId}`);
      const currentBalance = await tx.userBalance.findUnique({
        where: { userId },
        select: { formalInterviewCredits: true, updatedAt: true }
      });

      console.log(`💰 DEBUG: Current balance:`, currentBalance);

      if (!currentBalance || currentBalance.formalInterviewCredits <= 0) {
        console.error(`❌ DEBUG: Insufficient credits for user ${userId}:`, currentBalance);
        throw new Error('正式面试次数不足，请先充值');
      }

      // 2. 扣费（使用乐观锁）
      console.log(`💰 DEBUG: Decrementing credits for user ${userId}`);
      const updatedBalance = await tx.userBalance.update({
        where: {
          userId,
          updatedAt: currentBalance.updatedAt // 乐观锁
        },
        data: {
          formalInterviewCredits: {
            decrement: 1
          }
        }
      });
      console.log(`💰 DEBUG: Credits decremented, new balance:`, updatedBalance);

      // 3. 获取岗位信息
      let companyName: string | null = null;
      let positionName: string | null = null;
      let titleJobInfo = "Job Info from Client"; // 默认值

      if (positionId) {
        console.log(`🏢 DEBUG: Fetching position info for ${positionId}`);
        const position = await tx.targetPosition.findFirst({
          where: {
            id: positionId,
            userId: userId, // 确保用户只能访问自己的岗位
          },
          select: {
            positionName: true,
            companyName: true,
          }
        });

        console.log(`🏢 DEBUG: Position found:`, position);

        if (position) {
          companyName = position.companyName;
          positionName = position.positionName;
          titleJobInfo = `${position.positionName} - ${position.companyName}`;
          console.log(`🏢 DEBUG: Title job info: ${titleJobInfo}`);
        }
      } else {
        console.log(`🏢 DEBUG: No positionId provided, using default title`);
      }

      // 4. 创建面试会话记录（初始状态为pending）
      console.log(`📝 DEBUG: Creating interview session with id: ${sessionId}`);
      const interviewSession = await tx.interviewSession.create({
        data: {
          id: sessionId,
          userId: userId,
          status: 'pending', // 🔥 初始状态为pending
          titleJobInfo,
          companyName,
          positionName,
          startedAt: null, // pending状态下startedAt为null
        }
      });

      // 5. 创建消费记录
      await tx.usageRecord.create({
        data: {
          userId: userId,
          type: UsageType.FORMAL_INTERVIEW,
          amount: -1, // 负数表示消耗
          reason: `正式面试 - ${titleJobInfo}`,
        }
      });

      return {
        session: interviewSession,
        newBalance: updatedBalance
      };
    }, {
      timeout: 10000, // 10秒超时
      isolationLevel: 'ReadCommitted'
    });

    // 6. 在Redis中标记会话状态
    const redis = RedisService.getInstance();
    const redisClient = redis.getClient();
    await redisClient.set(`session:${sessionId}:status`, 'pending', 'EX', 3600); // 1小时过期

    console.log(`✅ Formal interview session created: ${sessionId} for user ${userId}`);

    return res.status(200).json({
      success: true,
      message: '面试会话创建成功',
      data: {
        sessionId: result.session.id,
        status: result.session.status,
        position: {
          companyName: result.session.companyName,
          positionName: result.session.positionName,
          titleJobInfo: result.session.titleJobInfo
        },
        newBalance: {
          formalInterviewCredits: result.newBalance.formalInterviewCredits,
          mockInterviewCredits: result.newBalance.mockInterviewCredits,
          mianshijunBalance: result.newBalance.mianshijunBalance
        }
      }
    });

  } catch (error: any) {
    console.error(`❌ Failed to start formal interview:`, error);
    
    if (error.message.includes('正式面试次数不足')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: '启动面试失败，请稍后重试'
    });
  }
});

/**
 * 结束面试 - 手动结束面试会话
 * POST /api/interviews/:sessionId/end
 */
router.post('/:sessionId/end', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { sessionId } = req.params;

    console.log(`🔚 Ending interview session ${sessionId} for user ${userId}`);

    // 验证会话归属
    const session = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId: userId
      }
    });

    if (!session) {
      return res.status(404).json({
        success: false,
        message: '面试会话不存在或无权限访问'
      });
    }

    if (session.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: '面试会话已结束'
      });
    }

    // 🔥 使用新的状态机流程：标记为closing，推送到清理队列
    const redis = RedisService.getInstance();
    const redisClient = redis.getClient();
    
    // 标记会话为closing状态
    await redisClient.set(`session:${sessionId}:status`, 'closing', 'EX', 600); // 10分钟过期
    
    // 推送到清理队列（这里直接调用CleanupService的方法）
    // 注意：在实际生产环境中，应该通过依赖注入获取CleanupService实例
    await redisClient.lpush('session-cleanup-queue', sessionId);

    console.log(`✅ Interview session ${sessionId} marked for cleanup`);

    return res.status(200).json({
      success: true,
      message: '面试已结束，正在保存数据...',
      data: {
        sessionId,
        status: 'closing'
      }
    });

  } catch (error: any) {
    console.error(`❌ Failed to end interview session:`, error);
    return res.status(500).json({
      success: false,
      message: '结束面试失败，请稍后重试'
    });
  }
});

/**
 * 获取面试会话状态
 * GET /api/interviews/:sessionId/status
 */
router.get('/:sessionId/status', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { sessionId } = req.params;

    // 首先从Redis检查状态
    const redis = RedisService.getInstance();
    const redisClient = redis.getClient();
    const redisStatus = await redisClient.get(`session:${sessionId}:status`);

    // 从数据库获取会话信息
    const session = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId: userId
      },
      select: {
        id: true,
        status: true,
        createdAt: true,
        startedAt: true,
        endedAt: true,
        companyName: true,
        positionName: true,
        titleJobInfo: true
      }
    });

    if (!session) {
      return res.status(404).json({
        success: false,
        message: '面试会话不存在或无权限访问'
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        sessionId: session.id,
        status: redisStatus || session.status, // Redis状态优先
        createdAt: session.createdAt,
        startedAt: session.startedAt,
        endedAt: session.endedAt,
        position: {
          companyName: session.companyName,
          positionName: session.positionName,
          titleJobInfo: session.titleJobInfo
        }
      }
    });

  } catch (error: any) {
    console.error(`❌ Failed to get interview session status:`, error);
    return res.status(500).json({
      success: false,
      message: '获取会话状态失败'
    });
  }
});

/**
 * 🔥 新增API：检查会话状态 - 用于前端重连时的状态轮询
 * GET /api/interviews/check-session/:sessionId
 */
router.get('/check-session/:sessionId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user!.userId;

    console.log(`🔍 Checking session status for ${sessionId}, user: ${userId}`);

    // 先查Redis缓存（快速路径）
    const redisClient = redisService.getClient();
    const cachedStatus = await redisClient.get(`session_status:${sessionId}`);

    if (cachedStatus) {
      console.log(`🔍 Session ${sessionId} status from Redis cache: ${cachedStatus}`);
      return res.json({
        success: true,
        status: cachedStatus,
        source: 'cache'
      });
    }

    // Redis未命中，查询数据库
    const session = await prisma.interviewSession.findFirst({
      where: {
        id: sessionId,
        userId: userId // 确保用户只能查询自己的会话
      },
      select: {
        status: true,
        createdAt: true,
        endedAt: true
      }
    });

    let status: string;
    if (!session) {
      status = 'not_found';
    } else {
      status = session.status || 'active';
    }

    // 缓存结果到Redis（1分钟过期）
    if (status !== 'not_found') {
      await redisClient.set(`session_status:${sessionId}`, status, 'EX', 60);
    }

    console.log(`🔍 Session ${sessionId} status from database: ${status}`);

    return res.json({
      success: true,
      status: status,
      source: 'database',
      sessionInfo: session ? {
        createdAt: session.createdAt,
        endedAt: session.endedAt
      } : null
    });

  } catch (error: any) {
    console.error(`❌ Failed to check session status for ${req.params.sessionId}:`, error);
    return res.status(500).json({
      success: false,
      message: '检查会话状态失败',
      error: error.message
    });
  }
});

export default router;
