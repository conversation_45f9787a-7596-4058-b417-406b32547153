// WebSocket服务器核心
import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import * as url from 'url';
import jwt from 'jsonwebtoken';
import { AuthenticatedWebSocket } from '../../types/websocket.js';
import { SessionManager } from './sessionManager.js';
import { MessageHandler } from './messageHandler.js';
import { AudioProcessor } from '../providers/audio/audioProcessor.js';
import { AISuggestionService } from './aiSuggestionService.js';
import { MockInterviewService } from './mockInterviewService.js';
import { ASRServiceManager } from '../providers/asr/asrServiceManager.js';
import { FFmpegProcessor } from '../providers/audio/ffmpegProcessor.js';
import { backendConnectionTracker } from '../../utils/ConnectionTracker';
import { sessionConnectionManager } from '../managers/SessionConnectionManager.js';
import { systemMonitor } from '../../monitoring/SystemMonitor.js';
// 🔥 新增：导入Redis和Prisma用于状态检查
import { RedisService } from '../../services/redisService.js';
import prisma from '../../lib/prisma.js';

export class WebSocketServerManager {
  private wss: WebSocketServer;
  private sessionManager: SessionManager;
  private messageHandler: MessageHandler;
  private audioProcessor: AudioProcessor;
  private aiSuggestionService: AISuggestionService;
  private mockInterviewService: MockInterviewService;
  private asrServiceManager: ASRServiceManager;
  private ffmpegProcessor: FFmpegProcessor;
  // 🔥 新增：Redis服务用于状态检查
  private redisService: RedisService;

  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';

  constructor() {
    console.log('🚀 Initializing WebSocket Server Manager...');

    // 🔥 优化：优先初始化核心服务
    this.sessionManager = new SessionManager();
    this.mockInterviewService = new MockInterviewService();
    this.redisService = new RedisService(); // 🔥 新增：初始化Redis服务
    console.log('✅ Core services initialized (SessionManager, MockInterviewService, RedisService)');

    // 🔥 优化：延迟初始化音频相关服务（仅在需要时初始化）
    this.initializeAudioServicesLazy();

    this.messageHandler = new MessageHandler(
      this.sessionManager,
      this.audioProcessor, // 可能为null，MessageHandler需要处理
      this.aiSuggestionService,
      this.mockInterviewService
    );

    // 创建WebSocket服务器
    this.wss = new WebSocketServer({ noServer: true });

    // 🔥 启动系统监控
    systemMonitor.start();
    console.log('🔍 System monitoring started');

    console.log('✅ WebSocket Server Manager initialized (optimized for fast startup)');
  }

  /**
   * 🔥 延迟初始化音频服务（仅在live模式需要时初始化）
   */
  private initializeAudioServicesLazy(): void {
    // 暂时设置为null，在需要时再初始化
    this.asrServiceManager = null as any;
    this.ffmpegProcessor = null as any;
    this.audioProcessor = null as any;
    this.aiSuggestionService = null as any;

    console.log('🔄 Audio services will be initialized on demand');
  }

  /**
   * 🔥 按需初始化音频服务（当live模式连接时调用）
   */
  private async ensureAudioServicesInitialized(): Promise<void> {
    if (this.asrServiceManager) {
      return; // 已初始化
    }

    console.log('🔄 Initializing audio services on demand...');
    this.asrServiceManager = new ASRServiceManager();
    this.ffmpegProcessor = new FFmpegProcessor();
    this.audioProcessor = new AudioProcessor(this.asrServiceManager);
    this.aiSuggestionService = new AISuggestionService();

    // 更新MessageHandler的依赖
    this.messageHandler.updateAudioServices(
      this.audioProcessor,
      this.aiSuggestionService
    );

    console.log('✅ Audio services initialized on demand');
  }

  /**
   * 设置HTTP服务器的WebSocket升级处理
   */
  setupWebSocketUpgrade(httpServer: Server): void {
    httpServer.on('upgrade', (request, socket, head) => {
      const pathname = request.url ? url.parse(request.url).pathname : undefined;
      const requestUrl = request.url ? new URL(request.url, `http://${request.headers.host}`) : null;
      const token = requestUrl?.searchParams.get('token');
      const mode = requestUrl?.searchParams.get('mode') as 'live' | 'mock' || 'live'; // 默认为live模式
      const positionId = requestUrl?.searchParams.get('positionId'); // 新增：获取岗位ID

      // 正则匹配路径 /api/ws/interview/:sessionId
      const interviewPathRegex = /^\/api\/ws\/interview\/([a-zA-Z0-9_-]+)$/;
      const match = pathname ? interviewPathRegex.exec(pathname) : null;

      if (match && token) {
        const sessionIdFromPath = match[1];
        console.log(`🔗 WebSocket upgrade request for session: ${sessionIdFromPath}, mode: ${mode}, positionId: ${positionId || 'none'}`);

        this.wss.handleUpgrade(request, socket, head, (ws) => {
          const authenticatedWs = ws as AuthenticatedWebSocket;
          this.wss.emit('connection', authenticatedWs, request, sessionIdFromPath, token, mode, positionId);
        });
      } else {
        console.log(`❌ WebSocket upgrade rejected for path ${pathname} (invalid path or missing token)`);
        socket.destroy();
      }
    });

    // 设置连接处理
    this.wss.on('connection', async (ws: AuthenticatedWebSocket, req, sessionIdFromPath?: string, token?: string, mode?: 'live' | 'mock', positionId?: string) => {
      await this.handleConnection(ws, sessionIdFromPath, token, mode, positionId);
    });

    console.log('✅ WebSocket upgrade handler configured');
  }

  /**
   * 🔥 新方法：检查会话状态 - 先查Redis缓存，再查数据库
   */
  private async checkSessionStatus(sessionId: string): Promise<'active' | 'pending_cleanup' | 'completed' | 'not_found'> {
    try {
      const redisClient = this.redisService.getClient();

      // 先查Redis缓存（快速路径）
      const cachedStatus = await redisClient.get(`session_status:${sessionId}`);
      if (cachedStatus) {
        console.log(`🔍 Session ${sessionId} status from Redis cache: ${cachedStatus}`);
        return cachedStatus as 'active' | 'pending_cleanup' | 'completed' | 'not_found';
      }

      // Redis未命中，查询数据库（一致性保证）
      const session = await prisma.interviewSession.findUnique({
        where: { id: sessionId },
        select: { status: true }
      });

      let dbStatus: string;
      if (!session) {
        dbStatus = 'not_found';
      } else {
        dbStatus = session.status || 'active';
      }

      // 缓存结果到Redis（1分钟过期）
      if (dbStatus !== 'not_found') {
        await redisClient.set(`session_status:${sessionId}`, dbStatus, 'EX', 60);
      }

      console.log(`🔍 Session ${sessionId} status from database: ${dbStatus}`);
      return dbStatus as 'active' | 'pending_cleanup' | 'completed' | 'not_found';

    } catch (error) {
      console.error(`❌ Error checking session status for ${sessionId}:`, error);
      // 发生错误时，保守处理：允许连接但记录错误
      return 'active';
    }
  }

  /**
   * 处理WebSocket连接 - 🔥 修复版本：添加状态检查
   */
  private async handleConnection(ws: AuthenticatedWebSocket, sessionId?: string, token?: string, mode: 'live' | 'mock' = 'live', positionId?: string): Promise<void> {
    if (!sessionId || !token) {
      console.error('❌ Session ID or Token missing in WebSocket connection');
      ws.close(1008, 'Session ID or Token missing');
      return;
    }

    console.log(`🔗 WebSocket connection attempt for session: ${sessionId}`);

    // 🔍 追踪WebSocket连接
    backendConnectionTracker.trackConnection(
      sessionId,
      'websocket',
      'WebSocketServerManager.handleConnection',
      { token: token.substring(0, 10) + '...', timestamp: Date.now() }
    );

    try {
      // 验证Token
      const decoded = jwt.verify(token, this.JWT_SECRET) as { userId: string };
      const userId = decoded.userId;

      if (!userId) {
        console.error('❌ User ID not found in token');
        ws.close(1008, 'User ID missing in token');
        return;
      }

      // 🔥 核心修复：连接前检查会话状态，避免连接到"幽灵"会话
      const sessionStatus = await this.checkSessionStatus(sessionId);
      if (sessionStatus === 'pending_cleanup' || sessionStatus === 'completed') {
        console.warn(`⚠️ Rejecting connection to session ${sessionId} with status: ${sessionStatus}`);
        ws.close(1008, `Session ${sessionStatus}, please start a new interview`);
        return;
      }

      // 🔥 修复：对于not_found的会话，允许连接（可能是新会话）
      if (sessionStatus === 'not_found') {
        console.log(`🆕 Session ${sessionId} not found in database, allowing new session creation`);
      } else {
        console.log(`✅ Session ${sessionId} status check passed: ${sessionStatus}`);
      }

      // 🔥 修复：Mock模式也需要音频服务来支持语音输入功能
      await this.ensureAudioServicesInitialized();

      if (mode === 'live') {
        // Live模式需要DashScope连接
        try {
          const sessionConnection = await sessionConnectionManager.createSessionConnection(sessionId, userId);
          console.log(`🔗 Created dedicated DashScope connection for session: ${sessionId}`);
        } catch (error) {
          console.error('❌ Failed to create session connection:', { sessionId, userId, error });
          ws.close(1011, 'Failed to create session connection');
          return;
        }
      } else {
        // Mock模式不需要DashScope连接，但需要音频服务来支持语音输入
        console.log(`🎯 Mock mode detected, audio services initialized for voice input support: ${sessionId}`);
      }

      // 创建会话
      await this.sessionManager.createSession(sessionId, userId, ws, positionId);

      // 🔥 存储会话模式信息
      (ws as any).interviewMode = mode;
      console.log(`✅ WebSocket client (User: ${userId}) connected to session: ${sessionId}, mode: ${mode}`);

      // 🔍 更新连接状态为已连接
      backendConnectionTracker.updateConnectionStatus(sessionId, 'connected', { userId });

      // 根据模式决定是否测试系统组件
      if (mode === 'live') {
        // Live模式需要测试系统组件
        await this.sendSystemStatus(ws, sessionId);
      } else {
        // Mock模式跳过系统测试，先设置监听器，然后发送欢迎消息
        // 🔥 添加WebSocket事件监听器调试
        console.log(`🔍 [${sessionId}] Setting up WebSocket event listeners...`);

        // 设置消息处理（移动到这里，先设置监听器）
        ws.on('message', async (message) => {
          console.log(`🔍 [${sessionId}] ⚡ Raw WebSocket message received! type: ${typeof message}, length: ${message.length}`);
          console.log(`🔍 [${sessionId}] WebSocket readyState: ${ws.readyState}`);
          console.log(`🔍 [${sessionId}] User ID: ${ws.userId}`);

          if (typeof message === 'string') {
            console.log(`🔍 [${sessionId}] String message preview: ${message.substring(0, 200)}...`);
            try {
              const parsed = JSON.parse(message);
              console.log(`🔍 [${sessionId}] ✅ Parsed message type: ${parsed.type}`);
            } catch (e) {
              console.log(`🔍 [${sessionId}] ❌ Failed to parse as JSON: ${e.message}`);
            }
          } else if (message instanceof Buffer) {
            console.log(`🔍 [${sessionId}] Buffer message, attempting to parse as string...`);
            try {
              const str = message.toString('utf8');
              const parsed = JSON.parse(str);
              console.log(`🔍 [${sessionId}] ✅ Buffer->JSON message type: ${parsed.type}`);
            } catch (e) {
              console.log(`🔍 [${sessionId}] ❌ Buffer not JSON: ${e.message}`);
            }
          }

          try {
            await this.messageHandler.handleMessage(ws, message);
            console.log(`✅ [${sessionId}] Message handled successfully`);
          } catch (error) {
            console.error(`❌ [${sessionId}] Message handling failed:`, error);
          }
        });

        console.log(`✅ [${sessionId}] WebSocket message listener attached`);

        // 现在发送欢迎消息（监听器已设置好）
        await this.sendMockWelcomeMessage(ws, sessionId);
      }

      // 🔥 Live模式的监听器设置（保持原有逻辑）
      if (mode === 'live') {
        // 🔥 添加WebSocket事件监听器调试
        console.log(`🔍 [${sessionId}] Setting up WebSocket event listeners...`);

        // 设置消息处理
        ws.on('message', async (message) => {
          console.log(`🔍 [${sessionId}] ⚡ Raw WebSocket message received! type: ${typeof message}, length: ${message.length}`);
          console.log(`🔍 [${sessionId}] WebSocket readyState: ${ws.readyState}`);
          console.log(`🔍 [${sessionId}] User ID: ${ws.userId}`);

          if (typeof message === 'string') {
            console.log(`🔍 [${sessionId}] String message preview: ${message.substring(0, 200)}...`);
            try {
              const parsed = JSON.parse(message);
              console.log(`🔍 [${sessionId}] ✅ Parsed message type: ${parsed.type}`);
            } catch (e) {
              console.log(`🔍 [${sessionId}] ❌ Failed to parse as JSON: ${e.message}`);
            }
          } else if (message instanceof Buffer) {
            console.log(`🔍 [${sessionId}] Buffer message, attempting to parse as string...`);
            try {
              const str = message.toString('utf8');
              const parsed = JSON.parse(str);
              console.log(`🔍 [${sessionId}] ✅ Buffer->JSON message type: ${parsed.type}`);
            } catch (e) {
              console.log(`🔍 [${sessionId}] ❌ Buffer not JSON: ${e.message}`);
            }
          }

          try {
            await this.messageHandler.handleMessage(ws, message);
            console.log(`✅ [${sessionId}] Message handled successfully`);
          } catch (error) {
            console.error(`❌ [${sessionId}] Message handling failed:`, error);
          }
        });

        console.log(`✅ [${sessionId}] WebSocket message listener attached`);
      }

      // 🔥 添加其他事件监听器的调试
      ws.on('ping', () => {
        console.log(`🏓 [${sessionId}] Ping received`);
      });

      ws.on('pong', () => {
        console.log(`🏓 [${sessionId}] Pong received`);
      });

      // 设置断开处理
      ws.on('close', async () => {
        await this.handleDisconnection(ws, sessionId);
      });

      // 设置错误处理
      ws.on('error', (error) => {
        console.error(`❌ WebSocket error for session ${sessionId}:`, error);
      });

    } catch (error) {
      console.error('❌ WebSocket connection authentication failed:', error);
      ws.close(1008, 'Authentication failed');
    }
  }

  /**
   * 发送系统状态 (Live模式)
   */
  private async sendSystemStatus(ws: AuthenticatedWebSocket, sessionId: string): Promise<void> {
    console.log('🔧 Testing system components...');

    // 测试FFmpeg
    const ffmpegWorking = await this.ffmpegProcessor.testFFmpeg();

    // 获取ASR状态报告
    const asrStatus = this.asrServiceManager.getStatusReport();

    console.log('📊 System Status Report:');
    console.log(`  - FFmpeg: ${ffmpegWorking ? 'Working' : 'Not Available'}`);
    Object.entries(asrStatus).forEach(([service, status]) => {
      console.log(`  - ${service}: ${status}`);
    });

    // 发送状态给客户端
    const statusMessage = {
      type: 'system_status',
      ffmpeg: ffmpegWorking,
      asr_status: asrStatus,
      message: `Welcome! Connected to session: ${sessionId}`
    };

    ws.send(JSON.stringify(statusMessage));
  }

  /**
   * 发送Mock模式欢迎消息
   */
  private async sendMockWelcomeMessage(ws: AuthenticatedWebSocket, sessionId: string): Promise<void> {
    console.log('🎯 Mock mode: Skipping system tests, sending welcome message...');

    // 发送Mock模式欢迎消息
    const welcomeMessage = {
      type: 'system_status',
      ffmpeg: true, // Mock模式不需要FFmpeg，但前端可能需要这个字段
      asr_status: { mock: 'ready' },
      message: `Welcome to Mock Interview! Connected to session: ${sessionId}`
    };

    ws.send(JSON.stringify(welcomeMessage));
    console.log('✅ Mock welcome message sent successfully');
  }

  /**
   * 处理WebSocket断开连接
   */
  private async handleDisconnection(ws: AuthenticatedWebSocket, sessionId: string): Promise<void> {
    console.log(`🔌 WebSocket client disconnected from session: ${sessionId}`);

    // 🔍 更新连接状态为已断开
    backendConnectionTracker.updateConnectionStatus(sessionId, 'disconnected');

    try {
      // 🔥 关闭会话级DashScope连接
      await sessionConnectionManager.closeSessionConnection(sessionId, 'WebSocket disconnected');
      console.log(`🔗 Closed dedicated DashScope connection for session: ${sessionId}`);

      // 清理消息处理器资源
      this.messageHandler.cleanupSession(sessionId);

      // 移除客户端
      await this.sessionManager.removeClient(ws);

      console.log(`✅ Session ${sessionId} cleanup completed`);
    } catch (error) {
      console.error(`❌ Error during session ${sessionId} cleanup:`, error);
    }
  }

  /**
   * 获取服务器统计信息
   */
  getServerStats(): any {
    return {
      activeConnections: this.wss.clients.size,
      activeSessions: this.sessionManager.getActiveSessionCount(),
      asrProviders: this.asrServiceManager.getAllProviders().length,
      availableAsrProviders: this.asrServiceManager.getAvailableProviders().length
    };
  }

  /**
   * 优雅关闭服务器
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down WebSocket Server...');
    
    try {
      // 关闭所有WebSocket连接
      this.wss.clients.forEach((ws) => {
        ws.close(1001, 'Server shutting down');
      });
      
      // 销毁所有服务
      await this.asrServiceManager.destroy();
      await this.aiSuggestionService.destroy();
      this.ffmpegProcessor.cleanup();

      // 🔥 停止系统监控
      systemMonitor.stop();
      console.log('🔍 System monitoring stopped');

      // 关闭WebSocket服务器
      this.wss.close();
      
      console.log('✅ WebSocket Server shutdown completed');
    } catch (error) {
      console.error('❌ Error during WebSocket Server shutdown:', error);
    }
  }
}
